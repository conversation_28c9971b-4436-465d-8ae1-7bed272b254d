dotnet ef dbcontext scaffold "Server=103.162.21.216;Database=heroAccount;User Id=yghero_user;Password=****************************************;TrustServerCertificate=True;" Microsoft.EntityFrameworkCore.SqlServer -o Database/Entities/Account -c AccountDbContext --force

dotnet ef dbcontext scaffold "Server=103.162.21.216;Database=heroGame;User Id=yghero_user;Password=****************************************;TrustServerCertificate=True;" Microsoft.EntityFrameworkCore.SqlServer -o Database/Entities/Game -c GameDbContext --force

dotnet ef dbcontext scaffold "Server=103.162.21.216;Database=heroPublic;User Id=yghero_user;Password=****************************************;TrustServerCertificate=True;" Microsoft.EntityFrameworkCore.SqlServer -o Database/Entities/Public -c PublicDbContext --force