﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblXwwlOpen
{
    public int FldPid { get; set; }

    public int FldPidx { get; set; }

    public int? FldNumber { get; set; }

    public string FldName { get; set; } = null!;

    public string FldNamex { get; set; } = null!;

    public int FldPp { get; set; }

    public int? FldMagic1 { get; set; }

    public int? FldMagic2 { get; set; }

    public int? FldMagic3 { get; set; }

    public int? FldMagic4 { get; set; }

    public int? FldMagic5 { get; set; }

    public int? FldFjThucTinh { get; set; }

    public int? FldFjTienHoa { get; set; }

    public int? FldFjTrungCapPhuHon { get; set; }

    public int? FldBd { get; set; }

    public int? FldDays { get; set; }

    public int? CoMoThongBao { get; set; }

    public int? SttHopEvent { get; set; }
}
