﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblXwwlMonster
{
    public int FldPid { get; set; }

    public string FldName { get; set; } = null!;

    public int FldLevel { get; set; }

    public int FldHp { get; set; }

    public int? FldAt { get; set; }

    public int? FldDf { get; set; }

    public int? FldExp { get; set; }

    public int? FldBoss { get; set; }

    public int? FldAuto { get; set; }

    public int? FldNpc { get; set; }

    public int? FldQuest { get; set; }

    public int? FldQuestid { get; set; }

    public int? FldStages { get; set; }

    public int? FldQuestitem { get; set; }

    public int? FldPp { get; set; }
}
