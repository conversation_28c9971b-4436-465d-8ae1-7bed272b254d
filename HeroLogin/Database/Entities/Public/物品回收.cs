﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class 物品回收
{
    public string? Id { get; set; }

    public string? 需要物品 { get; set; }

    public string? 武勋 { get; set; }

    public string? 元宝 { get; set; }

    public string? 积分 { get; set; }

    public string? 生命 { get; set; }

    public string? 攻击 { get; set; }

    public string? 防御 { get; set; }

    public string? 回避 { get; set; }

    public string? 命中 { get; set; }

    public string? 内功 { get; set; }

    public string? 金钱 { get; set; }

    public string? 套装id { get; set; }

    public string? 单件物品 { get; set; }

    public string? 说明 { get; set; }
}
