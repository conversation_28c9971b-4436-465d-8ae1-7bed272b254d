﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class CfgAbility
{
    public int Abid { get; set; }

    public string? Abname { get; set; }

    public string? Abdesc1 { get; set; }

    public string? Abdesc2 { get; set; }

    public string? Abdesc3 { get; set; }

    public string? Abdesc4 { get; set; }

    public string? Abdesc5 { get; set; }

    public string? Abdesc6 { get; set; }

    public string? Abdesc7 { get; set; }

    public string? Abdesc8 { get; set; }

    public string? Abdesc9 { get; set; }

    public string? Abdesc10 { get; set; }

    public string? Abdesc11 { get; set; }

    public int? Abtypeandjob { get; set; }

    public string? AbnameTrans { get; set; }

    public string? Abdesc1Trans { get; set; }

    public string? Abdesc2Trans { get; set; }

    public string? Abdesc3Trans { get; set; }

    public string? Abdesc4Trans { get; set; }

    public string? Abdesc5Trans { get; set; }

    public string? Abdesc6Trans { get; set; }

    public string? Abdesc7Trans { get; set; }

    public string? Abdesc8Trans { get; set; }

    public string? Abdesc9Trans { get; set; }

    public string? Abdesc10Trans { get; set; }

    public string? Abdesc11Trans { get; set; }
}
