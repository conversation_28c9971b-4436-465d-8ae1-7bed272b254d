﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblXwwlMission
{
    public int FldId { get; set; }

    public int FldPid { get; set; }

    public string? FldName { get; set; }

    public int? FldLevel { get; set; }

    public int? FldZx { get; set; }

    public int? FldNpcid { get; set; }

    public string? FldNpcname { get; set; }

    public string? FldNeedItem { get; set; }

    public int? FldStages { get; set; }

    public string? FldMsg { get; set; }

    public int? FldOn { get; set; }

    public int? FldMap { get; set; }

    public int? FldX { get; set; }

    public int? FldY { get; set; }

    public int? FldType { get; set; }

    public int? FldJob { get; set; }

    public string? FldGetItem { get; set; }

    public byte[]? FldData { get; set; }
}
