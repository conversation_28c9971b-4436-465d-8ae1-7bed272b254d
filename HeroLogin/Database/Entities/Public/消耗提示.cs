﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class 消耗提示
{
    public int? Id { get; set; }

    public int? 消耗最小 { get; set; }

    public int? 消耗最大 { get; set; }

    public int? 攻击 { get; set; }

    public int? 防御 { get; set; }

    public int? 血 { get; set; }

    public int? 气功 { get; set; }

    public float? 经验百分比 { get; set; }

    public float? 爆率百分比 { get; set; }

    public string? 上线提示 { get; set; }

    public int? 上线提示颜色 { get; set; }

    public string? 自定开头内容 { get; set; }
}
