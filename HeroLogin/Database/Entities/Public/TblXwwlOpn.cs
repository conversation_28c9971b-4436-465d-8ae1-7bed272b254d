﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblXwwlOpn
{
    public int FldIndex { get; set; }

    public int FldPid { get; set; }

    public string FldName { get; set; } = null!;

    public int FldPidx { get; set; }

    public string FldNamex { get; set; } = null!;

    public int FldNumber { get; set; }

    public int? FldMagic1 { get; set; }

    public int? FldMagic2 { get; set; }

    public int? FldMagic3 { get; set; }

    public int? FldMagic4 { get; set; }

    public int? FldMagic5 { get; set; }

    public int? FldFj觉醒 { get; set; }

    public int? FldFj进化 { get; set; }

    public int? FldFj中级附魂 { get; set; }

    public int? FldBd { get; set; }

    public int? FldDays { get; set; }

    public int FldPp { get; set; }
}
