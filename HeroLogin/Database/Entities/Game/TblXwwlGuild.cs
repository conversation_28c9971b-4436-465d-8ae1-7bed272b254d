﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Game;

public partial class TblXwwlGuild
{
    public int Id { get; set; }

    public string GName { get; set; } = null!;

    public string? GMaster { get; set; }

    public string? GNotice { get; set; }

    public int? Leve { get; set; }

    public int? ThanhDanh { get; set; }

    public byte[]? MonHuy { get; set; }

    public int? MonPhucWord { get; set; }

    public int? MonPhucMauSac { get; set; }

    public int? BangPhaiVoHuan { get; set; }

    public int? Thang { get; set; }

    public int? Thua { get; set; }

    public int? Hoa { get; set; }

    public string? MonPhaiTaiSan { get; set; }

    public int? ThongBaoCongThanh { get; set; }

    public string? LienMinhMinhChu { get; set; }
}
