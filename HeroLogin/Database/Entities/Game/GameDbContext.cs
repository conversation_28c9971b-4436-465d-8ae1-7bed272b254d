﻿
using HeroLogin.Core;
using Microsoft.EntityFrameworkCore;

namespace HeroYulgang.Database.Entities.Game;

public partial class GameDbContext : DbContext
{
    public GameDbContext()
    {
    }

    public GameDbContext(DbContextOptions<GameDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<BachBaoCacRecord> BachBaoCacRecords { get; set; }

    public virtual DbSet<BangChienTienDatCuoc> BangChienTienDatCuocs { get; set; }

    public virtual DbSet<CongThanhChienThanhChu> CongThanhChienThanhChus { get; set; }

    public virtual DbSet<EventTop> EventTops { get; set; }

    public virtual DbSet<EventTopDch> EventTopDches { get; set; }

    public virtual DbSet<ExchangeCharacter> ExchangeCharacters { get; set; }

    public virtual DbSet<Giftcode> Giftcodes { get; set; }

    public virtual DbSet<ItemRecord> ItemRecords { get; set; }

    public virtual DbSet<LogDeleteItem> LogDeleteItems { get; set; }

    public virtual DbSet<LogPk> LogPks { get; set; }

    public virtual DbSet<LogShop> LogShops { get; set; }

    public virtual DbSet<LogShopVoHuan> LogShopVoHuans { get; set; }

    public virtual DbSet<LogTheLucChien> LogTheLucChiens { get; set; }

    public virtual DbSet<LoginRecord> LoginRecords { get; set; }

    public virtual DbSet<LoginRecordMac> LoginRecordMacs { get; set; }

    public virtual DbSet<TblCheckdupleitem> TblCheckdupleitems { get; set; }

    public virtual DbSet<TblFactionQuestProgress> TblFactionQuestProgresses { get; set; }

    public virtual DbSet<TblGroupQuest> TblGroupQuests { get; set; }

    public virtual DbSet<TblGroupQuestContribution> TblGroupQuestContributions { get; set; }

    public virtual DbSet<TblGroupQuestContributionLog> TblGroupQuestContributionLogs { get; set; }

    public virtual DbSet<TblGroupQuestHistory> TblGroupQuestHistories { get; set; }

    public virtual DbSet<TblGuildQuestProgress> TblGuildQuestProgresses { get; set; }

    public virtual DbSet<TblSuDoSoLieu> TblSuDoSoLieus { get; set; }

    public virtual DbSet<TblTruyenThuHeThong> TblTruyenThuHeThongs { get; set; }

    public virtual DbSet<TblVinhDuHeThong> TblVinhDuHeThongs { get; set; }

    public virtual DbSet<TblXwwlChar> TblXwwlChars { get; set; }

    public virtual DbSet<TblXwwlCw> TblXwwlCws { get; set; }

    public virtual DbSet<TblXwwlCwarehouse> TblXwwlCwarehouses { get; set; }

    public virtual DbSet<TblXwwlGuild> TblXwwlGuilds { get; set; }

    public virtual DbSet<TblXwwlGuildMember> TblXwwlGuildMembers { get; set; }

    public virtual DbSet<TblXwwlPklog> TblXwwlPklogs { get; set; }

    public virtual DbSet<TblXwwlPublicWarehouse> TblXwwlPublicWarehouses { get; set; }

    public virtual DbSet<TblXwwlPvp> TblXwwlPvps { get; set; }

    public virtual DbSet<TblXwwlRoseTop> TblXwwlRoseTops { get; set; }

    public virtual DbSet<TblXwwlWarehouse> TblXwwlWarehouses { get; set; }

    public virtual DbSet<ThienMaThanCungDanhSach> ThienMaThanCungDanhSaches { get; set; }

    public virtual DbSet<ViewCheckLogin> ViewCheckLogins { get; set; }

    public virtual DbSet<VinhDuBangPhaiXepHang> VinhDuBangPhaiXepHangs { get; set; }

    public virtual DbSet<XepHangMonPhai> XepHangMonPhais { get; set; }

    public virtual DbSet<XepHangTheLuc> XepHangTheLucs { get; set; }

    public virtual DbSet<XwwlGameServerInfo> XwwlGameServerInfos { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        if (!optionsBuilder.IsConfigured)
        {
            var configManager = ConfigManager.Instance;
            optionsBuilder.UseSqlServer(configManager.ConnectionStrings.GameDb);
        }
    }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<BachBaoCacRecord>(entity =>
        {
            entity.ToTable("BachBaoCacRecord");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.UserId).HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(50);
            entity.Property(e => e.VatPhamId)
                .HasMaxLength(50)
                .HasColumnName("VatPham_ID");
            entity.Property(e => e.VatPhamTen).HasMaxLength(50);
        });

        modelBuilder.Entity<BangChienTienDatCuoc>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("BangChien_TienDatCuoc");

            entity.Property(e => e.BangPhaiId).HasColumnName("BangPhaiID");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.UserId).HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(50);
        });

        modelBuilder.Entity<CongThanhChienThanhChu>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("CongThanhChien_ThanhChu");

            entity.Property(e => e.BangPhaiId).HasColumnName("BangPhaiID");
            entity.Property(e => e.CongThanhBanThuongThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.CongThanhChienTenBang)
                .HasMaxLength(50)
                .HasColumnName("CongThanhChien_TenBang");
            entity.Property(e => e.CongThanhThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.TenThanhChu).HasMaxLength(50);
        });

        modelBuilder.Entity<EventTop>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_EventTop_copy1");

            entity.ToTable("EventTop");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.BangPhai).HasMaxLength(50);
            entity.Property(e => e.DiemChinhPhai).HasColumnName("Diem_ChinhPhai");
            entity.Property(e => e.DiemTaPhai).HasColumnName("Diem_TaPhai");
            entity.Property(e => e.PhanKhuTinTuc).HasMaxLength(50);
            entity.Property(e => e.TenNhanVat).HasMaxLength(50);
            entity.Property(e => e.TheLuc).HasMaxLength(50);
        });

        modelBuilder.Entity<EventTopDch>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__EventTop__3213E83F3D2C38DB");

            entity.ToTable("EventTop_DCH");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.BangPhai).HasMaxLength(50);
            entity.Property(e => e.DameTru).HasColumnName("Dame_Tru");
            entity.Property(e => e.DiemDchChinhPhai).HasColumnName("Diem_DCH_ChinhPhai");
            entity.Property(e => e.DiemDchTaPhai).HasColumnName("Diem_DCH_TaPhai");
            entity.Property(e => e.HopTacGietNguoi).HasDefaultValue(0);
            entity.Property(e => e.PhanKhuTinTuc).HasMaxLength(50);
            entity.Property(e => e.TenNhanVat).HasMaxLength(50);
            entity.Property(e => e.TheLuc).HasMaxLength(50);
        });

        modelBuilder.Entity<ExchangeCharacter>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__exchange__3213E83FCD04ADE6");

            entity.ToTable("exchangeCharacter");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.BuyerId)
                .HasMaxLength(255)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("buyer_id");
            entity.Property(e => e.Character)
                .HasMaxLength(255)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("character");
            entity.Property(e => e.CreatedAt)
                .HasColumnType("datetime")
                .HasColumnName("created_at");
            entity.Property(e => e.SellerId)
                .HasMaxLength(255)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("seller_id");
            entity.Property(e => e.Status)
                .HasMaxLength(255)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("status");
            entity.Property(e => e.UpdatedAt)
                .HasColumnType("datetime")
                .HasColumnName("updated_at");
        });

        modelBuilder.Entity<Giftcode>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK_TBL_GiftCode");

            entity.ToTable("GIFTCODE");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Cash).HasDefaultValue(0);
            entity.Property(e => e.DaSuDung)
                .HasDefaultValue(0)
                .HasColumnName("Da_su_dung");
            entity.Property(e => e.Giftcode1)
                .HasMaxLength(50)
                .HasDefaultValue("XXXXXXXXXX")
                .HasColumnName("Giftcode");
            entity.Property(e => e.NguoiSuDung)
                .HasMaxLength(50)
                .HasColumnName("Nguoi_su_dung");
            entity.Property(e => e.NoiDung)
                .HasMaxLength(50)
                .HasColumnName("Noi_dung");
            entity.Property(e => e.Username).HasMaxLength(50);
        });

        modelBuilder.Entity<ItemRecord>(entity =>
        {
            entity.ToTable("ItemRecord");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.GlobalId)
                .HasMaxLength(50)
                .HasColumnName("Global_ID");
            entity.Property(e => e.LoaiHinh).HasMaxLength(50);
            entity.Property(e => e.ThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.ToUserId).HasMaxLength(50);
            entity.Property(e => e.ToUserName).HasMaxLength(50);
            entity.Property(e => e.UserId).HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(50);
            entity.Property(e => e.VatPhamId)
                .HasMaxLength(50)
                .HasColumnName("VatPham_ID");
            entity.Property(e => e.VatPhamTen).HasMaxLength(50);
            entity.Property(e => e.VatPhamThuocTinh).HasMaxLength(100);
        });

        modelBuilder.Entity<LogDeleteItem>(entity =>
        {
            entity.HasKey(e => new { e.Id, e.MaItem });

            entity.ToTable("Log_DeleteItem");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.Iditem).HasColumnName("IDItem");
            entity.Property(e => e.LevelItem)
                .HasMaxLength(50)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.Thoigian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.Username).HasMaxLength(50);
        });

        modelBuilder.Entity<LogPk>(entity =>
        {
            entity.ToTable("LogPK");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Nguoibigiet).HasMaxLength(50);
            entity.Property(e => e.Nguoigiet).HasMaxLength(50);
            entity.Property(e => e.Thoigian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<LogShop>(entity =>
        {
            entity.ToTable("LogShop");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Cashconlai).HasColumnName("CASHCONLAI");
            entity.Property(e => e.CoHayKhongMoRa)
                .HasDefaultValue(0)
                .HasColumnName("co_hay_khong_mo_ra");
            entity.Property(e => e.DaSuDung)
                .HasDefaultValue(0)
                .HasColumnName("Da_su_dung");
            entity.Property(e => e.Iditem).HasColumnName("IDItem");
            entity.Property(e => e.Magic1).HasColumnName("MAGIC1");
            entity.Property(e => e.Magic2).HasColumnName("MAGIC2");
            entity.Property(e => e.Magic3).HasColumnName("MAGIC3");
            entity.Property(e => e.Magic4).HasColumnName("MAGIC4");
            entity.Property(e => e.Magic5).HasColumnName("MAGIC5");
            entity.Property(e => e.MuaId).HasColumnName("mua_id");
            entity.Property(e => e.TenItem).HasMaxLength(50);
            entity.Property(e => e.ThanhCong)
                .HasDefaultValue(0)
                .HasColumnName("Thanh_cong");
            entity.Property(e => e.Thoigian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.UserName).HasMaxLength(50);
            entity.Property(e => e.Userid).HasMaxLength(50);
        });

        modelBuilder.Entity<LogShopVoHuan>(entity =>
        {
            entity.ToTable("LogShopVoHuan");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CoHayKhongMoRa).HasColumnName("co_hay_khong_mo_ra");
            entity.Property(e => e.DaSuDung).HasColumnName("Da_su_dung");
            entity.Property(e => e.Iditem).HasColumnName("IDItem");
            entity.Property(e => e.Magic1).HasColumnName("MAGIC1");
            entity.Property(e => e.Magic2).HasColumnName("MAGIC2");
            entity.Property(e => e.Magic3).HasColumnName("MAGIC3");
            entity.Property(e => e.Magic4).HasColumnName("MAGIC4");
            entity.Property(e => e.Magic5).HasColumnName("MAGIC5");
            entity.Property(e => e.MuaId).HasColumnName("mua_id");
            entity.Property(e => e.TenItem).HasMaxLength(50);
            entity.Property(e => e.ThanhCong).HasColumnName("Thanh_cong");
            entity.Property(e => e.Thoigian).HasColumnType("datetime");
            entity.Property(e => e.UserName).HasMaxLength(50);
            entity.Property(e => e.Userid).HasMaxLength(50);
            entity.Property(e => e.Vohuanconlai).HasColumnName("VOHUANCONLAI");
        });

        modelBuilder.Entity<LogTheLucChien>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("Log_TheLucChien");

            entity.Property(e => e.Chet).HasColumnName("CHET");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.Giet).HasColumnName("GIET");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.Monphai)
                .HasMaxLength(50)
                .HasColumnName("MONPHAI");
            entity.Property(e => e.Ngay).HasColumnName("NGAY");
            entity.Property(e => e.Theluc)
                .HasMaxLength(50)
                .HasColumnName("THELUC");
        });

        modelBuilder.Entity<LoginRecord>(entity =>
        {
            entity.ToTable("LoginRecord");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.LoaiHinh).HasMaxLength(50);
            entity.Property(e => e.MacAddress).HasColumnName("Mac_Address");
            entity.Property(e => e.ThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.UserId).HasMaxLength(50);
            entity.Property(e => e.UserIp).HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(50);
        });

        modelBuilder.Entity<LoginRecordMac>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__LoginRec__3214EC27ACEAE920");

            entity.ToTable("LoginRecord_MAC");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.LoaiHinh).HasMaxLength(50);
            entity.Property(e => e.MacAddress).HasColumnName("Mac_Address");
            entity.Property(e => e.ServerId).HasColumnName("ServerID");
            entity.Property(e => e.ThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.UserId).HasMaxLength(50);
            entity.Property(e => e.UserIp).HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(50);
        });

        modelBuilder.Entity<TblCheckdupleitem>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TBL_CHEC__3214EC275F68EECB");

            entity.ToTable("TBL_CHECKDUPLEITEM");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CharName).HasMaxLength(255);
            entity.Property(e => e.ItemGlobalId)
                .HasColumnType("numeric(18, 0)")
                .HasColumnName("ItemGlobalID");
            entity.Property(e => e.ItemId)
                .HasColumnType("numeric(18, 0)")
                .HasColumnName("ItemID");
            entity.Property(e => e.UserId)
                .HasMaxLength(255)
                .HasColumnName("UserID");
        });

        modelBuilder.Entity<TblFactionQuestProgress>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TBL_FACT__3214EC27A62FE462");

            entity.ToTable("TBL_FACTION_QUEST_PROGRESS");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AcceptedTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CancelledTime).HasColumnType("datetime");
            entity.Property(e => e.CompletedTime).HasColumnType("datetime");
            entity.Property(e => e.FactionId).HasColumnName("FactionID");
            entity.Property(e => e.LastCompletedTime).HasColumnType("datetime");
            entity.Property(e => e.LastResetTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastUpdateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.QuestId).HasColumnName("QuestID");
            entity.Property(e => e.Status).HasDefaultValue((byte)1);

            entity.HasOne(d => d.Quest).WithMany(p => p.TblFactionQuestProgresses)
                .HasForeignKey(d => d.QuestId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_FACTION_QUEST_PROGRESS_QUEST");
        });

        modelBuilder.Entity<TblGroupQuest>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TBL_GROU__3214EC273956D07F");

            entity.ToTable("TBL_GROUP_QUEST");

            entity.Property(e => e.Id)
                .ValueGeneratedNever()
                .HasColumnName("ID");
            entity.Property(e => e.CreateDate)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.IsActive).HasDefaultValue(true);
            entity.Property(e => e.QuestDesc).HasMaxLength(500);
            entity.Property(e => e.QuestName).HasMaxLength(100);
            entity.Property(e => e.TargetId).HasColumnName("TargetID");
        });

        modelBuilder.Entity<TblGroupQuestContribution>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TBL_GROU__3214EC27949393AE");

            entity.ToTable("TBL_GROUP_QUEST_CONTRIBUTION");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ActionType).HasDefaultValue(1);
            entity.Property(e => e.ContributionTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FactionId).HasColumnName("FactionID");
            entity.Property(e => e.GuildId).HasColumnName("GuildID");
            entity.Property(e => e.PlayerId).HasColumnName("PlayerID");
            entity.Property(e => e.PlayerName).HasMaxLength(50);
            entity.Property(e => e.ProgressId).HasColumnName("ProgressID");
            entity.Property(e => e.QuestId).HasColumnName("QuestID");
            entity.Property(e => e.TargetId).HasColumnName("TargetID");
            entity.Property(e => e.TargetName).HasMaxLength(50);

            entity.HasOne(d => d.Quest).WithMany(p => p.TblGroupQuestContributions)
                .HasForeignKey(d => d.QuestId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_GROUP_QUEST_CONTRIBUTION_QUEST");
        });

        modelBuilder.Entity<TblGroupQuestContributionLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TBL_GROU__3214EC27443D0747");

            entity.ToTable("TBL_GROUP_QUEST_CONTRIBUTION_LOG");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ActionType).HasDefaultValue(1);
            entity.Property(e => e.ContributionId).HasColumnName("ContributionID");
            entity.Property(e => e.ContributionTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.PlayerId).HasColumnName("PlayerID");
            entity.Property(e => e.PlayerName).HasMaxLength(50);
            entity.Property(e => e.ProgressId).HasColumnName("ProgressID");
            entity.Property(e => e.TargetId).HasColumnName("TargetID");
            entity.Property(e => e.TargetName).HasMaxLength(50);

            entity.HasOne(d => d.Contribution).WithMany(p => p.TblGroupQuestContributionLogs)
                .HasForeignKey(d => d.ContributionId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_GROUP_QUEST_CONTRIBUTION_LOG_CONTRIBUTION");
        });

        modelBuilder.Entity<TblGroupQuestHistory>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TBL_GROU__3214EC2710109763");

            entity.ToTable("TBL_GROUP_QUEST_HISTORY");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.CompletedBy).HasMaxLength(50);
            entity.Property(e => e.CompletedTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.FactionId).HasColumnName("FactionID");
            entity.Property(e => e.GuildId).HasColumnName("GuildID");
            entity.Property(e => e.GuildName).HasMaxLength(50);
            entity.Property(e => e.QuestId).HasColumnName("QuestID");

            entity.HasOne(d => d.Quest).WithMany(p => p.TblGroupQuestHistories)
                .HasForeignKey(d => d.QuestId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_GROUP_QUEST_HISTORY_QUEST");
        });

        modelBuilder.Entity<TblGuildQuestProgress>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("PK__TBL_GUIL__3214EC276263CF21");

            entity.ToTable("TBL_GUILD_QUEST_PROGRESS");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.AcceptedTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.CancelledTime).HasColumnType("datetime");
            entity.Property(e => e.CompletedTime).HasColumnType("datetime");
            entity.Property(e => e.GuildId).HasColumnName("GuildID");
            entity.Property(e => e.LastCompletedTime).HasColumnType("datetime");
            entity.Property(e => e.LastResetTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.LastUpdateTime)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
            entity.Property(e => e.QuestId).HasColumnName("QuestID");
            entity.Property(e => e.Status).HasDefaultValue((byte)1);

            entity.HasOne(d => d.Quest).WithMany(p => p.TblGuildQuestProgresses)
                .HasForeignKey(d => d.QuestId)
                .OnDelete(DeleteBehavior.ClientSetNull)
                .HasConstraintName("FK_GUILD_QUEST_PROGRESS_QUEST");
        });

        modelBuilder.Entity<TblSuDoSoLieu>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_SuDoSoLieu");

            entity.Property(e => e.FldIndex).HasColumnName("FLD_INDEX");
            entity.Property(e => e.FldSname)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_SNAME");
            entity.Property(e => e.FldStlevel)
                .HasDefaultValue(1)
                .HasColumnName("FLD_STLEVEL");
            entity.Property(e => e.FldStwg1).HasColumnName("FLD_STWG1");
            entity.Property(e => e.FldStwg2).HasColumnName("FLD_STWG2");
            entity.Property(e => e.FldStwg3).HasColumnName("FLD_STWG3");
            entity.Property(e => e.FldStyhd).HasColumnName("FLD_STYHD");
            entity.Property(e => e.FldTlevel).HasColumnName("FLD_TLEVEL");
            entity.Property(e => e.FldTname)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_TNAME");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<TblTruyenThuHeThong>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_TruyenThuHeThong");

            entity.Property(e => e.GuiThuNpc).HasColumnName("GuiThu_NPC");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.NguoiGuiThuTen)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("NguoiGuiThu_Ten");
            entity.Property(e => e.NguoiNhanThuNhatVatTen)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("NguoiNhanThu_NhatVatTen");
            entity.Property(e => e.TruyenThuNoiDung)
                .HasMaxLength(2000)
                .IsUnicode(false);
            entity.Property(e => e.TruyenThuThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
        });

        modelBuilder.Entity<TblVinhDuHeThong>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_VinhDuHeThong");

            entity.Property(e => e.FldBangPhai)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_BangPhai");
            entity.Property(e => e.FldBangPhaiBangChu)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_BangPhai_BangChu");
            entity.Property(e => e.FldDangCap).HasColumnName("FLD_DangCap");
            entity.Property(e => e.FldDiemSo).HasColumnName("FLD_DiemSo");
            entity.Property(e => e.FldNgheNghiep).HasColumnName("FLD_NgheNghiep");
            entity.Property(e => e.FldTenNhanVat)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_TenNhanVat");
            entity.Property(e => e.FldTheLuc).HasColumnName("FLD_TheLuc");
            entity.Property(e => e.FldType).HasColumnName("FLD_Type");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<TblXwwlChar>(entity =>
        {
            entity.HasKey(e => new { e.Id, e.FldName })
                .HasName("PK_TBL_XWWL_Char_copy2")
                .HasFillFactor(90);

            entity.ToTable("TBL_XWWL_Char");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.BangPhaiDoCongHien)
                .HasDefaultValue(0)
                .HasColumnName("BangPhai_DoCongHien");
            entity.Property(e => e.BiGietSoLan).HasDefaultValue(0L);
            entity.Property(e => e.CongThanhChienThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("smalldatetime");
            entity.Property(e => e.FldAddAt).HasColumnName("FLD_ADD_AT");
            entity.Property(e => e.FldAddClvc)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ADD_CLVC");
            entity.Property(e => e.FldAddDf).HasColumnName("FLD_ADD_DF");
            entity.Property(e => e.FldAddHb).HasColumnName("FLD_ADD_HB");
            entity.Property(e => e.FldAddHp).HasColumnName("FLD_ADD_HP");
            entity.Property(e => e.FldAddKc)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ADD_KC");
            entity.Property(e => e.FldAddMp).HasColumnName("FLD_ADD_MP");
            entity.Property(e => e.FldAddMz).HasColumnName("FLD_ADD_MZ");
            entity.Property(e => e.FldAddPtvc)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ADD_PTVC");
            entity.Property(e => e.FldAsc7AntiQigong)
                .HasMaxLength(128)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_ASC7_ANTI_QIGONG");
            entity.Property(e => e.FldChtime)
                .HasMaxLength(320)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_CHTIME");
            entity.Property(e => e.FldCoatitem)
                .HasMaxLength(8000)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_COATITEM");
            entity.Property(e => e.FldConfig)
                .HasMaxLength(100)
                .HasDefaultValueSql("(0x)")
                .HasColumnName("FLD_CONFIG");
            entity.Property(e => e.FldCtime)
                .HasMaxLength(480)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_CTIME");
            entity.Property(e => e.FldCtimenew)
                .HasMaxLength(320)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_CTIMENEW");
            entity.Property(e => e.FldDayQuest)
                .HasMaxLength(1200)
                .HasDefaultValue("")
                .HasColumnName("FLD_DayQuest");
            entity.Property(e => e.FldDeTu)
                .HasMaxLength(60)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_DeTu");
            entity.Property(e => e.FldDoors)
                .HasMaxLength(1200)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_DOORS");
            entity.Property(e => e.FldExp)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValueSql("((0))")
                .HasColumnName("FLD_EXP");
            entity.Property(e => e.FldFace)
                .HasMaxLength(10)
                .HasColumnName("FLD_FACE");
            entity.Property(e => e.FldFashionItem)
                .HasMaxLength(5082)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_FASHION_ITEM");
            entity.Property(e => e.FldFbTime)
                .HasDefaultValue(0)
                .HasColumnName("FLD_FB_TIME");
            entity.Property(e => e.FldFightExp)
                .HasDefaultValue(5000)
                .HasColumnName("FLD_FIGHT_EXP");
            entity.Property(e => e.FldFqid)
                .HasMaxLength(20)
                .IsUnicode(false)
                .HasDefaultValue("d1")
                .HasColumnName("FLD_FQID");
            entity.Property(e => e.FldGetWx)
                .HasDefaultValue(0)
                .HasColumnName("FLD_GET_WX");
            entity.Property(e => e.FldGiaiTruThoiGian)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValue("2014/1/1 23:55:44")
                .HasColumnName("FLD_GiaiTruThoiGian");
            entity.Property(e => e.FldHdTime)
                .HasDefaultValue(0)
                .HasColumnName("FLD_HD_TIME");
            entity.Property(e => e.FldHits)
                .HasMaxLength(250)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_HITS");
            entity.Property(e => e.FldHp)
                .HasDefaultValue(128)
                .HasColumnName("FLD_HP");
            entity.Property(e => e.FldId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ID");
            entity.Property(e => e.FldIndex).HasColumnName("FLD_INDEX");
            entity.Property(e => e.FldItem)
                .HasMaxLength(8000)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_ITEM");
            entity.Property(e => e.FldJ9)
                .HasDefaultValue(0)
                .HasColumnName("FLD_J9");
            entity.Property(e => e.FldJhDate)
                .HasDefaultValue(new DateTime(2012, 12, 20, 10, 10, 0, 0, DateTimeKind.Unspecified))
                .HasColumnType("datetime")
                .HasColumnName("FLD_JH_DATE");
            entity.Property(e => e.FldJl)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_JL");
            entity.Property(e => e.FldJob).HasColumnName("FLD_JOB");
            entity.Property(e => e.FldJobLevel)
                .HasDefaultValue(0)
                .HasColumnName("FLD_JOB_LEVEL");
            entity.Property(e => e.FldJq)
                .HasDefaultValue(0)
                .HasColumnName("FLD_JQ");
            entity.Property(e => e.FldKhuonMat)
                .HasMaxLength(4)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_KhuonMat");
            entity.Property(e => e.FldKieuToc)
                .HasMaxLength(4)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_KieuToc");
            entity.Property(e => e.FldKongfu)
                .HasMaxLength(1404)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_KONGFU");
            entity.Property(e => e.FldLevel)
                .HasDefaultValue(1)
                .HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldLjkongfu)
                .HasMaxLength(128)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_LJKONGFU");
            entity.Property(e => e.FldLostWx)
                .HasDefaultValue(0)
                .HasColumnName("FLD_LOST_WX");
            entity.Property(e => e.FldLoveWord)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValueSql("((0))")
                .HasColumnName("FLD_LOVE_WORD");
            entity.Property(e => e.FldLumpid).HasColumnName("FLD_LUMPID");
            entity.Property(e => e.FldMaritalStatus)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MARITAL_STATUS");
            entity.Property(e => e.FldMarried)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MARRIED");
            entity.Property(e => e.FldMenow)
                .HasDefaultValue(101)
                .HasColumnName("FLD_MENOW");
            entity.Property(e => e.FldMlz)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MLZ");
            entity.Property(e => e.FldMoney)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValueSql("((100000))")
                .HasColumnName("FLD_MONEY");
            entity.Property(e => e.FldMoneyextralevel)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MONEYEXTRALEVEL");
            entity.Property(e => e.FldMp)
                .HasDefaultValue(116)
                .HasColumnName("FLD_MP");
            entity.Property(e => e.FldNametype)
                .HasMaxLength(48)
                .HasDefaultValueSql("(0x000000000000000000000000000000000100000000000000000000000000000002000000000000000000000000000000)")
                .HasColumnName("FLD_NAMETYPE");
            entity.Property(e => e.FldNszitem)
                .HasMaxLength(600)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_NSZITEM");
            entity.Property(e => e.FldNtcitem)
                .HasMaxLength(600)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_NTCITEM");
            entity.Property(e => e.FldOnline).HasColumnName("FLD_ONLINE");
            entity.Property(e => e.FldPinkbagItem)
                .HasMaxLength(2304)
                .HasDefaultValueSql("(0x)")
                .HasColumnName("FLD_PINKBAG_ITEM");
            entity.Property(e => e.FldPoint)
                .HasDefaultValue(0)
                .HasColumnName("FLD_POINT");
            entity.Property(e => e.FldPvpPiont)
                .HasDefaultValue(0)
                .HasColumnName("FLD_PVP_Piont");
            entity.Property(e => e.FldQitem)
                .HasMaxLength(300)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_QITEM");
            entity.Property(e => e.FldQlDu)
                .HasDefaultValue(0)
                .HasColumnName("FLD_QlDu");
            entity.Property(e => e.FldQlDuMax)
                .HasDefaultValue(0)
                .HasColumnName("FLD_QlDuMax");
            entity.Property(e => e.FldQlJzname)
                .HasMaxLength(50)
                .HasDefaultValue("")
                .HasColumnName("FLD_QlJZNAME");
            entity.Property(e => e.FldQlName)
                .HasMaxLength(50)
                .HasDefaultValue("")
                .HasColumnName("FLD_QlNAME");
            entity.Property(e => e.FldQlRank)
                .HasDefaultValue(10)
                .HasColumnName("FLD_QlRank");
            entity.Property(e => e.FldQuest)
                .HasMaxLength(1200)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_QUEST");
            entity.Property(e => e.FldQuestFinish)
                .HasMaxLength(1000)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_QUEST_FINISH");
            entity.Property(e => e.FldRoseTitlePoints)
                .HasDefaultValue(0)
                .HasColumnName("FLD_RoseTitlePoints");
            entity.Property(e => e.FldSe)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SE");
            entity.Property(e => e.FldSjjl).HasColumnName("FLD_SJJL");
            entity.Property(e => e.FldSkills)
                .HasMaxLength(100)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_SKILLS");
            entity.Property(e => e.FldSp)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SP");
            entity.Property(e => e.FldSpeakingType)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SpeakingType");
            entity.Property(e => e.FldStime)
                .HasMaxLength(320)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_STIME");
            entity.Property(e => e.FldSuDoVoCong)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_SuDoVoCong");
            entity.Property(e => e.FldSuPhu)
                .HasMaxLength(28)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_SuPhu");
            entity.Property(e => e.FldTaisinh).HasColumnName("FLD_TAISINH");
            entity.Property(e => e.FldThanNuVoCongDiemSo)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ThanNuVoCongDiemSo");
            entity.Property(e => e.FldThangThienKhiCong)
                .HasMaxLength(256)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_ThangThienKhiCong");
            entity.Property(e => e.FldThangThienLichLuyen)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ThangThienLichLuyen");
            entity.Property(e => e.FldThangThienVoCong)
                .HasMaxLength(256)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_ThangThienVoCong");
            entity.Property(e => e.FldThangThienVoCongDiemSo)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ThangThienVoCongDiemSo");
            entity.Property(e => e.FldTitlePoints)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TitlePoints");
            entity.Property(e => e.FldTlc)
                .HasDefaultValue(0)
                .HasColumnName("FLD_TLC");
            entity.Property(e => e.FldTongkim).HasColumnName("FLD_TONGKIM");
            entity.Property(e => e.FldVipAt).HasColumnName("FLD_VIP_AT");
            entity.Property(e => e.FldVipDf).HasColumnName("FLD_VIP_DF");
            entity.Property(e => e.FldVipHp).HasColumnName("FLD_VIP_HP");
            entity.Property(e => e.FldVipLevel).HasColumnName("FLD_VIP_LEVEL");
            entity.Property(e => e.FldVipdj).HasColumnName("FLD_VIPDJ");
            entity.Property(e => e.FldWearitem)
                .HasMaxLength(5000)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_WEARITEM");
            entity.Property(e => e.FldWhtb)
                .HasDefaultValue(0)
                .HasColumnName("FLD_WHTB");
            entity.Property(e => e.FldWx)
                .HasDefaultValue(100)
                .HasColumnName("FLD_WX");
            entity.Property(e => e.FldX)
                .HasDefaultValue(421.0)
                .HasColumnName("FLD_X");
            entity.Property(e => e.FldXb)
                .HasDefaultValue(0)
                .HasColumnName("FLD_XB");
            entity.Property(e => e.FldY)
                .HasDefaultValue(1868.0)
                .HasColumnName("FLD_Y");
            entity.Property(e => e.FldZ)
                .HasDefaultValue(15.0)
                .HasColumnName("FLD_Z");
            entity.Property(e => e.FldZbver)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ZBVER");
            entity.Property(e => e.FldZs).HasColumnName("FLD_ZS");
            entity.Property(e => e.FldZscs).HasColumnName("FLD_ZSCS");
            entity.Property(e => e.FldZx)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ZX");
            entity.Property(e => e.FldZzSl)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ZzSl");
            entity.Property(e => e.FldZzType)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ZzType");
            entity.Property(e => e.Fld七彩)
                .HasDefaultValue(0)
                .HasColumnName("FLD_七彩");
            entity.Property(e => e.Fld任务等级4).HasColumnName("FLD_任务等级4");
            entity.Property(e => e.Fld在线时间).HasColumnName("FLD_在线时间");
            entity.Property(e => e.Fld在线等级).HasColumnName("FLD_在线等级");
            entity.Property(e => e.Fld师傅)
                .HasMaxLength(50)
                .HasDefaultValue("")
                .HasColumnName("FLD_师傅");
            entity.Property(e => e.Fld师徒武功11).HasColumnName("FLD_师徒武功1_1");
            entity.Property(e => e.Fld师徒武功12).HasColumnName("FLD_师徒武功1_2");
            entity.Property(e => e.Fld师徒武功13).HasColumnName("FLD_师徒武功1_3");
            entity.Property(e => e.Fld徒弟1)
                .HasMaxLength(50)
                .HasDefaultValue("")
                .HasColumnName("FLD_徒弟1");
            entity.Property(e => e.Fld徒弟2)
                .HasMaxLength(50)
                .HasDefaultValue("")
                .HasColumnName("FLD_徒弟2");
            entity.Property(e => e.Fld徒弟3)
                .HasMaxLength(50)
                .HasDefaultValue("")
                .HasColumnName("FLD_徒弟3");
            entity.Property(e => e.Fld签名类型).HasColumnName("FLD_签名类型");
            entity.Property(e => e.Fld领奖标志).HasColumnName("FLD_领奖标志");
            entity.Property(e => e.Nhanqualandau).HasColumnName("NHANQUALANDAU");
            entity.Property(e => e.SoLanGietNguoi).HasDefaultValue(0L);
            entity.Property(e => e.TlcRandomPhe)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("TLC_RANDOM_PHE");
            entity.Property(e => e.Version).HasColumnName("VERSION");
            entity.Property(e => e.VohuanGioihanTheongay)
                .HasDefaultValue(0)
                .HasColumnName("VOHUAN_GIOIHAN_THEONGAY");
            entity.Property(e => e.VohuanTime)
                .HasMaxLength(50)
                .HasColumnName("VOHUAN_TIME");
            entity.Property(e => e._).HasColumnName(" ");
            entity.Property(e => e.在线时间)
                .HasMaxLength(200)
                .HasDefaultValueSql("(0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000)");
        });

        modelBuilder.Entity<TblXwwlCw>(entity =>
        {
            entity.HasKey(e => new { e.Id, e.ItmeId });

            entity.ToTable("TBL_XWWL_Cw");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.FldBs)
                .HasDefaultValue(0)
                .HasColumnName("FLD_BS");
            entity.Property(e => e.FldExp)
                .HasMaxLength(50)
                .HasDefaultValueSql("((0))")
                .HasColumnName("FLD_EXP");
            entity.Property(e => e.FldHp)
                .HasDefaultValue(0)
                .HasColumnName("FLD_HP");
            entity.Property(e => e.FldItem)
                .HasMaxLength(1168)
                .HasDefaultValueSql("(0x000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000)")
                .HasColumnName("FLD_ITEM");
            entity.Property(e => e.FldJob)
                .HasDefaultValue(1)
                .HasColumnName("FLD_JOB");
            entity.Property(e => e.FldJobLevel)
                .HasDefaultValue(0)
                .HasColumnName("FLD_JOB_LEVEL");
            entity.Property(e => e.FldKongfu)
                .HasMaxLength(130)
                .HasDefaultValueSql("(0x00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000)")
                .HasColumnName("FLD_KONGFU");
            entity.Property(e => e.FldLevel)
                .HasDefaultValue(1)
                .HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldMagic1)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldMp)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MP");
            entity.Property(e => e.FldSxbl)
                .HasDefaultValue(1000)
                .HasColumnName("FLD_SXBL");
            entity.Property(e => e.FldWearitem)
                .HasMaxLength(292)
                .HasDefaultValueSql("(0x0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000)")
                .HasColumnName("FLD_WEARITEM");
            entity.Property(e => e.FldZcd)
                .HasDefaultValue(1000)
                .HasColumnName("FLD_ZCD");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ZrName).HasMaxLength(50);
        });

        modelBuilder.Entity<TblXwwlCwarehouse>(entity =>
        {
            entity.HasKey(e => e.FldName);

            entity.ToTable("TBL_XWWL_CWarehouse");

            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ID");
            entity.Property(e => e.FldItem)
                .HasMaxLength(6000)
                .HasColumnName("FLD_ITEM");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<TblXwwlGuild>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TBL_XWWL_Guild_PK");

            entity.ToTable("TBL_XWWL_Guild");

            entity.HasIndex(e => new { e.Id, e.MonHuy }, "TBL_XWWL_Guild9");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.BangPhaiVoHuan).HasDefaultValue(0);
            entity.Property(e => e.GMaster)
                .HasMaxLength(50)
                .HasColumnName("G_Master");
            entity.Property(e => e.GName)
                .HasMaxLength(50)
                .HasColumnName("G_Name");
            entity.Property(e => e.GNotice)
                .HasMaxLength(60)
                .HasDefaultValue("")
                .HasColumnName("G_Notice");
            entity.Property(e => e.Hoa).HasDefaultValue(0);
            entity.Property(e => e.Leve).HasDefaultValue(1);
            entity.Property(e => e.LienMinhMinhChu)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasDefaultValueSql("((0))")
                .HasColumnName("LienMinh_MinhChu");
            entity.Property(e => e.MonHuy).HasMaxLength(800);
            entity.Property(e => e.MonPhaiTaiSan)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasDefaultValueSql("((0))");
            entity.Property(e => e.MonPhucMauSac).HasDefaultValue(0);
            entity.Property(e => e.MonPhucWord).HasDefaultValue(1);
            entity.Property(e => e.Thang).HasDefaultValue(0);
            entity.Property(e => e.ThanhDanh).HasDefaultValue(1);
            entity.Property(e => e.ThongBaoCongThanh)
                .HasDefaultValue(0)
                .HasColumnName("ThongBao_CongThanh");
            entity.Property(e => e.Thua).HasDefaultValue(0);
        });

        modelBuilder.Entity<TblXwwlGuildMember>(entity =>
        {
            entity.HasKey(e => new { e.Id, e.FldName })
                .HasName("PK_GuildMember")
                .HasFillFactor(90);

            entity.ToTable("TBL_XWWL_GuildMember");

            entity.HasIndex(e => e.GName, "TBL_XWWL_GuildMember8");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldGuildPoint).HasColumnName("FLD_GuildPoint");
            entity.Property(e => e.FldLevel)
                .HasDefaultValue(1)
                .HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldNewGuildPoint).HasColumnName("FLD_NewGuildPoint");
            entity.Property(e => e.GName)
                .HasMaxLength(50)
                .HasColumnName("G_Name");
            entity.Property(e => e.Leve).HasDefaultValue(1);
        });

        modelBuilder.Entity<TblXwwlPklog>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_PKLog");

            entity.Property(e => e.FldDeath)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_DEATH");
            entity.Property(e => e.FldDeathGuild)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_DEATH_GUILD");
            entity.Property(e => e.FldKiller)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_KILLER");
            entity.Property(e => e.FldKillerGuild)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_KILLER_GUILD");
            entity.Property(e => e.FldLasttime)
                .HasColumnType("smalldatetime")
                .HasColumnName("FLD_LASTTIME");
            entity.Property(e => e.FldNum).HasColumnName("FLD_NUM");
            entity.Property(e => e.FldWx).HasColumnName("FLD_WX");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<TblXwwlPublicWarehouse>(entity =>
        {
            entity.HasKey(e => new { e.Id, e.FldId }).HasFillFactor(90);

            entity.ToTable("TBL_XWWL_PublicWarehouse");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.FldId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ID");
            entity.Property(e => e.FldItem)
                .HasMaxLength(4560)
                .HasColumnName("FLD_ITEM");
            entity.Property(e => e.FldItime)
                .HasMaxLength(50)
                .HasDefaultValueSql("(0x00000000000000000000000000000000)")
                .HasColumnName("FLD_ITIME");
            entity.Property(e => e.FldMoney)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_MONEY");
            entity.Property(e => e.FldZbver)
                .HasDefaultValue(0)
                .HasColumnName("FLD_ZBVER");
        });

        modelBuilder.Entity<TblXwwlPvp>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_PVP");

            entity.Property(e => e.AChayTronSoLan)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("A_ChayTronSoLan");
            entity.Property(e => e.ANguoiChoi)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("A_NguoiChoi");
            entity.Property(e => e.AgietNguoiSoLuong)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("AGietNguoiSoLuong");
            entity.Property(e => e.AthuDuocNguyenBao)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("AThuDuocNguyenBao");
            entity.Property(e => e.BChayTronSoLan)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("B_ChayTronSoLan");
            entity.Property(e => e.BNguoiChoi)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("B_NguoiChoi");
            entity.Property(e => e.BgietNguoiSoLuong)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("BGietNguoiSoLuong");
            entity.Property(e => e.BthuDuocNguyenBao)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("BThuDuocNguyenBao");
            entity.Property(e => e.SanTapTen).HasMaxLength(50);
            entity.Property(e => e.TranhTaiKetQua)
                .HasMaxLength(10)
                .IsFixedLength();
        });

        modelBuilder.Entity<TblXwwlRoseTop>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_RoseTop");

            entity.Property(e => e.FldInnum).HasColumnName("FLD_INNUM");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldOutnum).HasColumnName("FLD_OUTNUM");
            entity.Property(e => e.FldSex).HasColumnName("FLD_SEX");
            entity.Property(e => e.FldZx).HasColumnName("FLD_ZX");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<TblXwwlWarehouse>(entity =>
        {
            entity.HasKey(e => new { e.Id, e.FldName }).HasFillFactor(90);

            entity.ToTable("TBL_XWWL_Warehouse");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ID");
            entity.Property(e => e.FldItem)
                .HasMaxLength(4560)
                .HasColumnName("FLD_ITEM");
            entity.Property(e => e.FldMoney)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_MONEY");
        });

        modelBuilder.Entity<ThienMaThanCungDanhSach>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ThienMaThanCung_DanhSach");

            entity.Property(e => e.BangChiemThanh)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("Bang_Chiem_Thanh");
            entity.Property(e => e.CongThanhCuongHoaLevel)
                .HasMaxLength(255)
                .IsUnicode(false)
                .HasColumnName("Cong_Thanh_CuongHoa_Level");
            entity.Property(e => e.NgayChiemThanh)
                .IsUnicode(false)
                .HasColumnName("Ngay_Chiem_Thanh");
            entity.Property(e => e.ThoiGianLamMoiCongThanh)
                .HasColumnType("datetime")
                .HasColumnName("ThoiGian_LamMoi_CongThanh");
        });

        modelBuilder.Entity<ViewCheckLogin>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("View_CheckLogin");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.LoaiHinh).HasMaxLength(50);
            entity.Property(e => e.MacAddress).HasColumnName("Mac_Address");
            entity.Property(e => e.ServerId).HasColumnName("ServerID");
            entity.Property(e => e.ThoiGian).HasColumnType("datetime");
            entity.Property(e => e.UserId).HasMaxLength(50);
            entity.Property(e => e.UserIp).HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(50);
        });

        modelBuilder.Entity<VinhDuBangPhaiXepHang>(entity =>
        {
            entity.HasKey(e => e.FldName).HasName("PK_荣誉排行门派");

            entity.ToTable("VinhDuBangPhaiXepHang");

            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldBp)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_BP");
            entity.Property(e => e.FldFq)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_FQ");
            entity.Property(e => e.FldJob).HasColumnName("FLD_JOB");
            entity.Property(e => e.FldJobLevel).HasColumnName("FLD_JOB_LEVEL");
            entity.Property(e => e.FldLevel).HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldRy).HasColumnName("FLD_RY");
            entity.Property(e => e.FldZx).HasColumnName("FLD_ZX");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.ThoiGian)
                .HasDefaultValueSql("(getdate())")
                .HasColumnType("datetime");
        });

        modelBuilder.Entity<XepHangMonPhai>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("XepHangMonPhai");

            entity.Property(e => e.FldId)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_ID");
            entity.Property(e => e.FldOnline).HasColumnName("FLD_ONLINE");
            entity.Property(e => e.GMaster)
                .HasMaxLength(50)
                .HasColumnName("G_Master");
            entity.Property(e => e.GName)
                .HasMaxLength(50)
                .HasColumnName("G_Name");
        });

        modelBuilder.Entity<XepHangTheLuc>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("XepHangTheLuc");

            entity.Property(e => e.BangHoi).HasMaxLength(50);
            entity.Property(e => e.TenNhanVat).HasMaxLength(50);
            entity.Property(e => e.TheLuc).HasMaxLength(50);
        });

        modelBuilder.Entity<XwwlGameServerInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("XWWL_GameServerInfo");

            entity.Property(e => e.Serverid).HasColumnName("serverid");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
