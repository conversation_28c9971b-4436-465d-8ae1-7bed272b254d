using System;

namespace RxjhServer.GroupQuest
{
    /// <summary>
    /// Class lưu trữ thông tin đóng góp của người chơi vào quest nhóm
    /// </summary>
    public class GroupQuestContribution
    {
        /// <summary>
        /// ID của bản ghi đóng góp
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// ID của quest
        /// </summary>
        public int QuestID { get; set; }

        /// <summary>
        /// ID của progress
        /// </summary>
        public int ProgressID { get; set; }

        /// <summary>
        /// ID của người chơi
        /// </summary>
        public int PlayerID { get; set; }

        /// <summary>
        /// Tên của người chơi
        /// </summary>
        public string PlayerName { get; set; }

        /// <summary>
        /// ID của guild (nếu là quest guild)
        /// </summary>
        public int? GuildID { get; set; }

        /// <summary>
        /// ID của faction (nếu là quest faction)
        /// </summary>
        public int? FactionID { get; set; }

        /// <summary>
        /// Loại hành động (1: Kill, 2: Collect, etc.)
        /// </summary>
        public int ActionType { get; set; } = 1;

        /// <summary>
        /// ID của mục tiêu (monster, player, etc.)
        /// </summary>
        public int? TargetID { get; set; }

        /// <summary>
        /// Tên của mục tiêu
        /// </summary>
        public string TargetName { get; set; }

        /// <summary>
        /// Số lượng đóng góp
        /// </summary>
        public int ContributionCount { get; set; }

        /// <summary>
        /// Thời gian đóng góp
        /// </summary>
        public DateTime ContributionTime { get; set; }

        /// <summary>
        /// Đã nhận phần thưởng chưa
        /// </summary>
        public bool HasReceivedReward { get; set; }

        /// <summary>
        /// Tính tỷ lệ đóng góp dựa trên tổng số đóng góp
        /// </summary>
        /// <param name="totalContribution">Tổng số đóng góp của tất cả người chơi</param>
        /// <returns>Tỷ lệ đóng góp từ 0 đến 1</returns>
        public float CalculateContributionRatio(int totalContribution)
        {
            if (totalContribution <= 0)
                return 0;

            return (float)ContributionCount / totalContribution;
        }

        /// <summary>
        /// Đánh dấu đã nhận phần thưởng
        /// </summary>
        public void MarkRewardReceived()
        {
            HasReceivedReward = true;
        }

        /// <summary>
        /// Tạo một bản ghi log cho đóng góp này
        /// </summary>
        /// <returns>Đối tượng GroupQuestContributionLog</returns>
        public GroupQuestContributionLog CreateLog()
        {
            return new GroupQuestContributionLog
            {
                ContributionID = ID,
                ProgressID = ProgressID,
                PlayerID = PlayerID,
                PlayerName = PlayerName,
                ActionType = ActionType,
                TargetID = TargetID,
                TargetName = TargetName,
                ContributionCount = ContributionCount,
                ContributionTime = ContributionTime
            };
        }
    }

    /// <summary>
    /// Class lưu trữ log đóng góp của người chơi
    /// </summary>
    public class GroupQuestContributionLog
    {
        /// <summary>
        /// ID của bản ghi log
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// ID của bản ghi đóng góp
        /// </summary>
        public int ContributionID { get; set; }

        /// <summary>
        /// ID của progress
        /// </summary>
        public int ProgressID { get; set; }

        /// <summary>
        /// ID của người chơi
        /// </summary>
        public int PlayerID { get; set; }

        /// <summary>
        /// Tên của người chơi
        /// </summary>
        public string PlayerName { get; set; }

        /// <summary>
        /// Loại hành động (1: Kill, 2: Collect, etc.)
        /// </summary>
        public int ActionType { get; set; } = 1;

        /// <summary>
        /// ID của mục tiêu (monster, player, etc.)
        /// </summary>
        public int? TargetID { get; set; }

        /// <summary>
        /// Tên của mục tiêu
        /// </summary>
        public string TargetName { get; set; }

        /// <summary>
        /// Số lượng đóng góp
        /// </summary>
        public int ContributionCount { get; set; }

        /// <summary>
        /// Thời gian đóng góp
        /// </summary>
        public DateTime ContributionTime { get; set; }
    }
}
