using System;
using HeroLogin.Services;

namespace RxjhServer.GroupQuest
{
    /// <summary>
    /// Lớp khởi tạo hệ thống quest nhóm
    /// </summary>
    public static class GroupQuestInitializer
    {
        /// <summary>
        /// Khởi tạo hệ thống quest nhóm
        /// </summary>
        public static void Initialize()
        {
            try
            {
                Logger.Instance.Info( "Đang khởi tạo hệ thống Group Quest...");

                // Khởi tạo manager
                GroupQuestManager.Instance.Initialize();

                // Đăng ký xử lý gói tin

                // Đăng ký hook sự kiện
                RegisterHooks();

                Logger.Instance.Info( "Đã khởi tạo hệ thống Group Quest thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi khởi tạo hệ thống Group Quest: {ex.Message}");
            }
        }

        /// <summary>
        /// Đăng ký hook sự kiện
        /// </summary>
        private static void RegisterHooks()
        {
            try
            {
                // Đăng ký hook sự kiện quái vật bị tiêu diệt
                // Tìm vị trí gọi GuiDiTuVongSoLieu trong NpcClass và thêm hook

                // Đăng ký hook sự kiện người chơi bị tiêu diệt
                // Tìm vị trí xử lý người chơi bị tiêu diệt trong Players và thêm hook

                Logger.Instance.Info( "Đã đăng ký hook sự kiện Group Quest");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi đăng ký hook sự kiện Group Quest: {ex.Message}");
            }
        }

        /// <summary>
        /// Tạo các quest mẫu
        /// </summary>
        public static void CreateSampleQuests()
        {
            try
            {
                // Kiểm tra xem đã có quest nào chưa
                var quests = GroupQuestManager.Instance.GetAllQuestDefinitions();

                if (quests.Count > 0)
                {
                    Logger.Instance.Info( $"Đã có {quests.Count} quest trong hệ thống, không tạo quest mẫu");
                    return;
                }

                Logger.Instance.Info( "Đang tạo các quest mẫu...");

                // Tạo quest guild - tiêu diệt người chơi
                GroupQuestDefinition guildPvpQuest = new GroupQuestDefinition
                {
                    ID = 1001, // ID tự nhập
                    QuestName = "Tiêu diệt kẻ địch",
                    QuestDesc = "Tiêu diệt 50 người chơi thuộc bang hội khác",
                    QuestType = GroupQuestType.Guild,
                    TargetType = TargetType.KillPlayers,
                    TargetCount = 50,
                    RequiredLevel = 0,
                    RewardExp = 1000000,
                    RewardMoney = 1000000,
                    RewardItem = 1000001390, // Bảo Châu 1
                    RewardItemCount = 1,
                    ResetType = ResetType.Daily,
                    IsActive = true
                };

                // Tạo quest guild - tiêu diệt quái vật
                GroupQuestDefinition guildPveQuest = new GroupQuestDefinition
                {
                    ID = 1002, // ID tự nhập
                    QuestName = "Săn quái vật",
                    QuestDesc = "Tiêu diệt 100 quái vật bất kỳ",
                    QuestType = GroupQuestType.Guild,
                    TargetType = TargetType.KillMonsters,
                    TargetCount = 100,
                    RequiredLevel = 0,
                    RewardExp = 500000,
                    RewardMoney = 500000,
                    RewardItem = 1000001391, // Bảo Châu 2
                    RewardItemCount = 1,
                    ResetType = ResetType.Daily,
                    IsActive = true
                };

                // Tạo quest guild - tiêu diệt boss
                GroupQuestDefinition guildBossQuest = new GroupQuestDefinition
                {
                    ID = 1003, // ID tự nhập
                    QuestName = "Tiêu diệt boss",
                    QuestDesc = "Tiêu diệt 1 boss thế giới",
                    QuestType = GroupQuestType.Guild,
                    TargetType = TargetType.KillBoss,
                    TargetID = 15423, // ID boss thế giới
                    TargetCount = 1,
                    RequiredLevel = 0,
                    RewardExp = 2000000,
                    RewardMoney = 2000000,
                    RewardItem = 1000001392, // Bảo Châu 3
                    RewardItemCount = 1,
                    ResetType = ResetType.Weekly,
                    IsActive = true
                };

                // Tạo quest faction - tiêu diệt người chơi
                GroupQuestDefinition factionPvpQuest = new GroupQuestDefinition
                {
                    ID = 2001, // ID tự nhập
                    QuestName = "Tiêu diệt kẻ địch",
                    QuestDesc = "Tiêu diệt 50 người chơi thuộc phe đối địch",
                    QuestType = GroupQuestType.Faction,
                    TargetType = TargetType.KillPlayers,
                    TargetCount = 50,
                    RequiredLevel = 0,
                    RewardExp = 1000000,
                    RewardMoney = 1000000,
                    RewardItem = 1000001393, // Bảo Châu 4
                    RewardItemCount = 1,
                    ResetType = ResetType.Daily,
                    IsActive = true
                };

                // Tạo quest faction - tiêu diệt quái vật
                GroupQuestDefinition factionPveQuest = new GroupQuestDefinition
                {
                    ID = 2002, // ID tự nhập
                    QuestName = "Săn quái vật",
                    QuestDesc = "Tiêu diệt 100 quái vật bất kỳ",
                    QuestType = GroupQuestType.Faction,
                    TargetType = TargetType.KillMonsters,
                    TargetCount = 100,
                    RequiredLevel = 0,
                    RewardExp = 500000,
                    RewardMoney = 500000,
                    RewardItem = 1000001394, // Bảo Châu 5
                    RewardItemCount = 1,
                    ResetType = ResetType.Daily,
                    IsActive = true
                };

                // Tạo quest faction - tiêu diệt boss
                GroupQuestDefinition factionBossQuest = new GroupQuestDefinition
                {
                    ID = 2003, // ID tự nhập
                    QuestName = "Tiêu diệt boss",
                    QuestDesc = "Tiêu diệt 1 boss thế giới",
                    QuestType = GroupQuestType.Faction,
                    TargetType = TargetType.KillBoss,
                    TargetID = 15423, // ID boss thế giới
                    TargetCount = 1,
                    RequiredLevel = 0,
                    RewardExp = 2000000,
                    RewardMoney = 2000000,
                    RewardItem = 1000001395, // Bảo Châu 6
                    RewardItemCount = 1,
                    ResetType = ResetType.Weekly,
                    IsActive = true
                };

                // Thêm vào database
                GroupQuestManager.Instance.AddQuestDefinition(guildPvpQuest);
                GroupQuestManager.Instance.AddQuestDefinition(guildPveQuest);
                GroupQuestManager.Instance.AddQuestDefinition(guildBossQuest);
                GroupQuestManager.Instance.AddQuestDefinition(factionPvpQuest);
                GroupQuestManager.Instance.AddQuestDefinition(factionPveQuest);
                GroupQuestManager.Instance.AddQuestDefinition(factionBossQuest);

                Logger.Instance.Info( "Đã tạo 6 quest mẫu thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info( $"Lỗi tạo quest mẫu: {ex.Message}");
            }
        }
    }
}
