using System;
using System.Collections.Generic;

namespace RxjhServer.GroupQuest
{
    /// <summary>
    /// Trạng thái của nhiệm vụ
    /// </summary>
    public enum QuestStatus
    {
        /// <summary>
        /// Đã nhận nhiệm vụ
        /// </summary>
        Accepted = 1,

        /// <summary>
        /// Đang thực hiện
        /// </summary>
        InProgress = 2,

        /// <summary>
        /// Đã hoàn thành
        /// </summary>
        Completed = 3,

        /// <summary>
        /// Đã hủy
        /// </summary>
        Cancelled = 4
    }

    /// <summary>
    /// Class lưu trữ tiến trình của một quest nhóm
    /// </summary>
    public class GroupQuestProgress
    {
        /// <summary>
        /// ID của bản ghi tiến trình
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// ID của quest
        /// </summary>
        public int QuestID { get; set; }

        /// <summary>
        /// ID của guild (nếu là quest guild)
        /// </summary>
        public int? GuildID { get; set; }

        /// <summary>
        /// ID của faction (nếu là quest faction)
        /// </summary>
        public int? FactionID { get; set; }

        /// <summary>
        /// Số lượng hiện tại đã hoàn thành
        /// </summary>
        public int CurrentCount { get; set; }

        /// <summary>
        /// Trạng thái nhiệm vụ: 1: Đã nhận, 2: Đang thực hiện, 3: Đã hoàn thành, 4: Đã hủy
        /// </summary>
        public QuestStatus Status { get; set; } = QuestStatus.Accepted;

        /// <summary>
        /// Thời điểm nhận nhiệm vụ
        /// </summary>
        public DateTime AcceptedTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Thời gian cập nhật cuối cùng
        /// </summary>
        public DateTime LastUpdateTime { get; set; }

        /// <summary>
        /// Thời gian hoàn thành
        /// </summary>
        public DateTime? CompletedTime { get; set; }

        /// <summary>
        /// Thời gian hủy nhiệm vụ
        /// </summary>
        public DateTime? CancelledTime { get; set; }

        /// <summary>
        /// Thời gian reset cuối cùng
        /// </summary>
        public DateTime LastResetTime { get; set; }

        /// <summary>
        /// Thời gian hoàn thành cuối cùng
        /// </summary>
        public DateTime? LastCompletedTime { get; set; }

        /// <summary>
        /// Kiểm tra xem nhiệm vụ đã hoàn thành chưa
        /// </summary>
        public bool IsCompleted => Status == QuestStatus.Completed;

        /// <summary>
        /// Thông tin quest
        /// </summary>
        public GroupQuestDefinition QuestDefinition { get; set; }

        /// <summary>
        /// Danh sách đóng góp của người chơi
        /// </summary>
        public List<GroupQuestContribution> PlayerContributions { get; set; } = new List<GroupQuestContribution>();

        /// <summary>
        /// Tăng số lượng hiện tại và kiểm tra hoàn thành
        /// </summary>
        /// <param name="amount">Số lượng cần tăng</param>
        /// <returns>True nếu quest đã hoàn thành sau khi tăng, False nếu chưa hoàn thành</returns>
        public bool IncrementCount(int amount = 1)
        {
            // Chỉ tăng số lượng nếu nhiệm vụ đang được nhận hoặc đang thực hiện
            if (Status != QuestStatus.Accepted && Status != QuestStatus.InProgress)
                return false;

            // Đánh dấu nhiệm vụ đang thực hiện
            if (Status == QuestStatus.Accepted)
                Status = QuestStatus.InProgress;

            CurrentCount += amount;
            LastUpdateTime = DateTime.Now;

            // Kiểm tra hoàn thành
            if (QuestDefinition != null && CurrentCount >= QuestDefinition.TargetCount)
            {
                Status = QuestStatus.Completed;
                CompletedTime = DateTime.Now;
                return true;
            }

            return false;
        }

        /// <summary>
        /// Thêm đóng góp của người chơi
        /// </summary>
        /// <param name="playerID">ID của người chơi</param>
        /// <param name="playerName">Tên của người chơi</param>
        /// <param name="amount">Số lượng đóng góp</param>
        /// <param name="actionType">Loại hành động (1: Kill, 2: Collect, etc.)</param>
        /// <param name="targetID">ID của mục tiêu (monster, player, etc.)</param>
        /// <param name="targetName">Tên của mục tiêu</param>
        /// <returns>Đối tượng đóng góp đã được cập nhật</returns>
        public GroupQuestContribution AddContribution(int playerID, string playerName, int amount = 1, int actionType = 1, int? targetID = null, string targetName = null)
        {
            // Tìm đóng góp hiện có
            var contribution = PlayerContributions.Find(c => c.PlayerName == playerName);

            if (contribution == null)
            {
                // Tạo mới nếu chưa có
                contribution = new GroupQuestContribution
                {
                    QuestID = QuestID,
                    ProgressID = ID,
                    PlayerID = playerID,
                    PlayerName = playerName,
                    GuildID = GuildID,
                    FactionID = FactionID,
                    ActionType = actionType,
                    TargetID = targetID,
                    TargetName = targetName,
                    ContributionCount = 0,
                    ContributionTime = DateTime.Now,
                    HasReceivedReward = false
                };
                PlayerContributions.Add(contribution);
            }

            // Cập nhật đóng góp
            contribution.ContributionCount += amount;
            contribution.ContributionTime = DateTime.Now;
            contribution.ActionType = actionType;
            contribution.TargetID = targetID;
            contribution.TargetName = targetName;

            return contribution;
        }

        /// <summary>
        /// Lấy tỷ lệ hoàn thành
        /// </summary>
        /// <returns>Tỷ lệ hoàn thành từ 0 đến 1</returns>
        public float GetCompletionRatio()
        {
            if (QuestDefinition == null || QuestDefinition.TargetCount <= 0)
                return 0;

            return (float)CurrentCount / QuestDefinition.TargetCount;
        }

        /// <summary>
        /// Lấy phần trăm hoàn thành
        /// </summary>
        /// <returns>Phần trăm hoàn thành từ 0 đến 100</returns>
        public int GetCompletionPercentage()
        {
            return (int)(GetCompletionRatio() * 100);
        }

        /// <summary>
        /// Reset tiến trình quest
        /// </summary>
        public void Reset()
        {
            // Lưu lại thời gian hoàn thành cuối cùng nếu đang ở trạng thái hoàn thành
            if (Status == QuestStatus.Completed && CompletedTime.HasValue)
            {
                LastCompletedTime = CompletedTime;
            }

            CurrentCount = 0;
            Status = QuestStatus.Accepted;
            LastResetTime = DateTime.Now;
            CompletedTime = null;
            CancelledTime = null;
            AcceptedTime = DateTime.Now;
            PlayerContributions.Clear();
        }

        /// <summary>
        /// Hủy nhiệm vụ
        /// </summary>
        public void Cancel()
        {
            Status = QuestStatus.Cancelled;
            CancelledTime = DateTime.Now;
        }
    }
}
