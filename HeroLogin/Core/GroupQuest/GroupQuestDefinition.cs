using System;

namespace RxjhServer.GroupQuest
{
    /// <summary>
    /// Đ<PERSON>nh nghĩa loại quest nhóm
    /// </summary>
    public enum GroupQuestType
    {
        Guild = 1,
        Faction = 2
    }

    /// <summary>
    /// Đ<PERSON>nh nghĩa loại mục tiêu của quest
    /// </summary>
    public enum TargetType
    {
        KillPlayers = 1,
        KillMonsters = 2,
        KillBoss = 3
    }

    /// <summary>
    /// Định nghĩa chu kỳ reset của quest
    /// </summary>
    public enum ResetType
    {
        Daily = 1,
        Weekly = 2,
        Monthly = 3,
        Never = 4
    }

    /// <summary>
    /// Class định nghĩa thông tin cơ bản của một quest nhóm
    /// </summary>
    public class GroupQuestDefinition
    {
        /// <summary>
        /// ID của quest
        /// </summary>
        public int ID { get; set; }

        /// <summary>
        /// Tên của quest
        /// </summary>
        public string QuestName { get; set; }

        /// <summary>
        /// <PERSON><PERSON> tả của quest
        /// </summary>
        public string QuestDesc { get; set; }

        /// <summary>
        /// Loại quest (Guild hoặc Faction)
        /// </summary>
        public GroupQuestType QuestType { get; set; }

        /// <summary>
        /// Loại mục tiêu (Kill Players, Kill Monsters, Kill Boss)
        /// </summary>
        public TargetType TargetType { get; set; }

        /// <summary>
        /// ID của mục tiêu (Monster ID, Boss ID, null nếu là kill player)
        /// </summary>
        public int? TargetID { get; set; }

        /// <summary>
        /// Số lượng cần tiêu diệt
        /// </summary>
        public int TargetCount { get; set; }

        /// <summary>
        /// Cấp độ yêu cầu
        /// </summary>
        public int RequiredLevel { get; set; }

        /// <summary>
        /// Kinh nghiệm thưởng
        /// </summary>
        public int RewardExp { get; set; }

        /// <summary>
        /// Tiền thưởng
        /// </summary>
        public long RewardMoney { get; set; }

        /// <summary>
        /// ID vật phẩm thưởng
        /// </summary>
        public int? RewardItem { get; set; }

        /// <summary>
        /// Số lượng vật phẩm thưởng
        /// </summary>
        public int? RewardItemCount { get; set; }

        /// <summary>
        /// Loại reset
        /// </summary>
        public ResetType ResetType { get; set; }

        /// <summary>
        /// Trạng thái kích hoạt
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Ngày tạo
        /// </summary>
        public DateTime CreateDate { get; set; }

        /// <summary>
        /// Kiểm tra xem quest có phải là quest guild không
        /// </summary>
        public bool IsGuildQuest => QuestType == GroupQuestType.Guild;

        /// <summary>
        /// Kiểm tra xem quest có phải là quest faction không
        /// </summary>
        public bool IsFactionQuest => QuestType == GroupQuestType.Faction;

        /// <summary>
        /// Lấy tên loại mục tiêu
        /// </summary>
        public string GetTargetTypeName()
        {
            return TargetType switch
            {
                TargetType.KillPlayers => "Tiêu diệt người chơi",
                TargetType.KillMonsters => "Tiêu diệt quái vật",
                TargetType.KillBoss => "Tiêu diệt boss",
                _ => "Không xác định"
            };
        }

        /// <summary>
        /// Lấy tên loại quest
        /// </summary>
        public string GetQuestTypeName()
        {
            return QuestType switch
            {
                GroupQuestType.Guild => "Nhiệm vụ bang hội",
                GroupQuestType.Faction => "Nhiệm vụ phe phái",
                _ => "Không xác định"
            };
        }

        /// <summary>
        /// Lấy tên chu kỳ reset
        /// </summary>
        public string GetResetTypeName()
        {
            return ResetType switch
            {
                ResetType.Daily => "Hàng ngày",
                ResetType.Weekly => "Hàng tuần",
                ResetType.Monthly => "Hàng tháng",
                ResetType.Never => "Không bao giờ",
                _ => "Không xác định"
            };
        }
    }
}
