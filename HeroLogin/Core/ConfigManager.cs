using System;
using System.IO;
using System.Text.Json;
using System.Collections.Generic;
using Microsoft.Extensions.Configuration;
using HeroLogin.Services;

namespace HeroLogin.Core
{
    public class ServerChannel
    {
        public string ServerName { get; set; } = string.Empty;
        public int ServerID { get; set; } = 1;
        public string ServerIP { get; set; } = "127.0.0.1";
        public int GameServerPort { get; set; } = 13000;
        public int GameServerGrpcPort { get; set; } = 7000;
        public int MaximumOnline { get; set; } = 1000;
        public int AutomaticConnectionTime { get; set; } = 60;
        public bool Status { get; set; } = true;
        public int Badge { get; set; } = 0;
        public int CurrentPlayers { get; set; } = 0;
    }

    public class ServerCluster
    {
        public int ID { get; set; } = 1;
        public string ClusterName { get; set; } = string.Empty;
        public List<ServerChannel> Channels { get; set; } = new List<ServerChannel>();
    }

    public class LoginServerSettings
    {
        public string LoginServerIP { get; set; } = "127.0.0.1";
        public int LoginServerGrpcPort { get; set; } = 6999;
        public int LoginServerPort { get; set; } = 1300;
    }

    public class AppSettings
    {
        public string Environment { get; set; } = "Production";
        public bool ShowPacketData { get; set; } = false;
        public string LogLevel { get; set; } = "Info";
    }

    public class DatabaseConfig
    {
        public string AccountDb { get; set; } = string.Empty;
        public string GameDb { get; set; } = string.Empty;
        public string PublicDb { get; set; } = string.Empty;

        public string BBGDb { get; set; } = string.Empty;
    }

    public class ConfigManager
    {
        private static ConfigManager? _instance;
        private IConfiguration _configuration = null!;

        public DatabaseConfig ConnectionStrings { get; private set; }
        public List<ServerCluster> ServerClusterSettings { get; private set; }
        public LoginServerSettings LoginServerSettings { get; private set; }
        public AppSettings AppSettings { get; private set; }

        public static ConfigManager Instance => _instance ??= new ConfigManager();

        private ConfigManager()
        {
            // Khởi tạo cấu hình mặc định
            ConnectionStrings = new DatabaseConfig();
            ServerClusterSettings = new List<ServerCluster>();
            LoginServerSettings = new LoginServerSettings();
            AppSettings = new AppSettings();

            try
            {
                // Đọc cấu hình từ appsettings.json
                _configuration = new ConfigurationBuilder()
                    .SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                    .Build();

                // Đọc cấu hình kết nối cơ sở dữ liệu
                var connectionSection = _configuration.GetSection("ConnectionStrings");
                if (connectionSection.Exists())
                {
                    ConnectionStrings.AccountDb = connectionSection["AccountDb"] ?? ConnectionStrings.AccountDb;
                    ConnectionStrings.GameDb = connectionSection["GameDb"] ?? ConnectionStrings.GameDb;
                    ConnectionStrings.PublicDb = connectionSection["PublicDb"] ?? ConnectionStrings.PublicDb;
                }

                // Đọc cấu hình server cluster
                var serverClustersSection = _configuration.GetSection("ServerClusterSettings");
                if (serverClustersSection.Exists())
                {
                    ServerClusterSettings = serverClustersSection.Get<List<ServerCluster>>() ?? ServerClusterSettings;
                }

                // Đọc cấu hình login server
                var loginServerSection = _configuration.GetSection("LoginServerSettings");
                if (loginServerSection.Exists())
                {
                    LoginServerSettings = loginServerSection.Get<LoginServerSettings>() ?? LoginServerSettings;
                }

                // Đọc cấu hình ứng dụng
                var appSettingsSection = _configuration.GetSection("AppSettings");
                if (appSettingsSection.Exists())
                {
                    AppSettings = appSettingsSection.Get<AppSettings>() ?? AppSettings;
                }

                Logger.Instance.Info("Đã tải cấu hình từ appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải cấu hình: {ex.Message}");
            }
        }

        public void SaveConfig()
        {
            try
            {
                var configData = new
                {
                    ConnectionStrings,
                    ServerClusterSettings,
                    LoginServerSettings,
                    AppSettings
                };

                string json = JsonSerializer.Serialize(configData, new JsonSerializerOptions
                {
                    WriteIndented = true
                });

                File.WriteAllText("appsettings.json", json);
                Logger.Instance.Info("Đã lưu cấu hình vào appsettings.json thành công");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi lưu cấu hình: {ex.Message}");
            }
        }
    }
}
