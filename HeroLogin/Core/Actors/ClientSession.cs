using System;
using System.Net;
using Akka.Actor;

namespace HeroLogin.Core.Actors
{
    /// <summary>
    /// <PERSON>ưu trữ thông tin về phiên kết n<PERSON>i của client
    /// </summary>
    public class ClientSession
    {
        public int SessionId { get; }
        public IPEndPoint RemoteEndPoint { get; }
        public DateTime ConnectedTime { get; }
        public DateTime LastActivityTime { get; private set; }
        public bool IsAuthenticated { get; set; }
        public string? AccountId { get; set; }
        public IActorRef Connection { get; }
        public string? SelectedServerId { get; set; }

        public PlayerStatus PlayerStatus { get; set; } = PlayerStatus.None;

        public ClientSession(int sessionId, EndPoint remoteEndPoint, IActorRef connection)
        {
            SessionId = sessionId;
            RemoteEndPoint = (IPEndPoint)remoteEndPoint;
            ConnectedTime = DateTime.Now;
            LastActivityTime = DateTime.Now;
            IsAuthenticated = false;
            Connection = connection;
            PlayerStatus = PlayerStatus.None;
        }

        public void UpdateActivity()
        {
            LastActivityTime = DateTime.Now;
        }
    }
}

public enum PlayerStatus
{
    None,
    Failed,
    LoggedIn,
    SelectingServer,
    SelectedServer,
    SelectingCharacter,
    InGame,
    OffTrade,
    OffAttack,
    Disconnected
}