using System;
using Akka.Actor;
using Akka.IO;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer;

namespace HeroYulgang.Core.Actors
{
    /// <summary>
    /// Actor x<PERSON> lý kết nối của một client cụ thể
    /// </summary>
    public class ClientActor : ReceiveActor
    {
        private readonly IActorRef _connection;
        private readonly ClientSession _session;
        private readonly IActorRef _packetHandlerActor;
        private Players _player;
        private ActorNetState _actorNetState;

        public ClientActor(IActorRef connection, ClientSession session, IActorRef packetHandlerActor)
        {
            _connection = connection;
            _session = session;
            _packetHandlerActor = packetHandlerActor;
            // World.list.Add(session.SessionId, this);

            // Tạo ActorNetState cho kết nối này
            _actorNetState = PlayerNetworkManager.CreateActorNetState(_connection, _session.SessionId, _session.RemoteEndPoint);

            // Đ<PERSON><PERSON> nghĩa các message handler
            Receive<Tcp.Received>(msg => HandleReceived(msg));
            Receive<Tcp.ConnectionClosed>(msg => HandleConnectionClosed(msg));
            Receive<SetPlayerReference>(msg => SetPlayerReference(msg));
        }

        private void SetPlayerReference(SetPlayerReference message)
        {
            _player = message.Player;

            // Thiết lập player context cho PacketHandlerActor thay vì tạo PlayerPacketHandlerActor riêng
            if (_player != null && _actorNetState != null)
            {
                // Cập nhật Client của Player để sử dụng ActorNetState
                PlayerNetworkManager.UpdatePlayerClient(_player, _actorNetState);

                // Gửi player context đến PacketHandlerActor
                _packetHandlerActor.Tell(new SetPlayerContext(_player, _actorNetState, _connection));

                Logger.Instance.Debug($"Đã thiết lập player context cho PacketHandlerActor cho người chơi {_player.UserName}");
            }
        }

        private void HandleReceived(Tcp.Received received)
        {
            try
            {
                // Cập nhật thời gian hoạt động
                _session.UpdateActivity();

                // Chuyển đổi ByteString thành byte array
                byte[] data = received.Data.ToArray();
                byte[] decryptedData = Utils.Crypto.DecryptPacket(data);

                // Ghi log gói tin nhận được nếu cần
                //PacketLogger.LogIncomingPacket(_session.SessionId, data);

                // Gửi packet đến PacketHandlerActor thống nhất
                if (_player != null)
                {
                    // Nếu đã có player, gửi ProcessPlayerPacket
                    Logger.Instance.Debug($"Chuyển tiếp packet đến PacketHandlerActor (ProcessPlayerPacket) cho session {_session.SessionId}");

                    _packetHandlerActor.Tell(new HeroYulgang.Core.Actors.ProcessPlayerPacket(decryptedData, decryptedData.Length));
                }
                else
                {
                    // Nếu chưa có player, gửi ProcessPacket thông thường
                    Logger.Instance.Debug($"Chuyển tiếp packet đến PacketHandlerActor (ProcessPacket) cho session {_session.SessionId}");

                    _packetHandlerActor.Tell(new ProcessPacket(_connection, _session, decryptedData));
                }
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi xử lý dữ liệu nhận được từ client {_session.SessionId}: {ex.Message}");
            }
        }

        private void HandleConnectionClosed(Tcp.ConnectionClosed message)
        {
            // Kết nối đã đóng, dừng actor
            Context.Stop(Self);
        }
    }

    /// <summary>
    /// Message để thiết lập tham chiếu đến đối tượng Player
    /// </summary>
    public class SetPlayerReference
    {
        public Players Player { get; }

        public SetPlayerReference(Players player)
        {
            Player = player;
        }
    }

    /// <summary>
    /// Message yêu cầu xử lý gói tin
    /// </summary>
    public class ProcessPacket
    {
        public IActorRef Connection { get; }
        public ClientSession Session { get; }
        public byte[] Data { get; }

        public ProcessPacket(IActorRef connection, ClientSession session, byte[] data)
        {
            Connection = connection;
            Session = session;
            Data = data;
        }
    }
}
