using RxjhServer.ManageZone;
using System;
using System.Timers;
using HeroYulgang.Helpers;

namespace RxjhServer;

public class X_Mat_Dat_Vat_Pham_Loai : IDisposable
{
	public Zone CurrentZone;
	private System.Timers.Timer npcyd;

	public ThreadSafeDictionary<int, Players> PlayList;

	public long id;

	private DateTime _time;

	private X_Vat_Pham_Loai _VatPham;

	private byte[] _VatPham_byte;

	private float _Rxjh_X;

	private float _Rxjh_Y;

	private float _Rxjh_Z;

	private int _Rxjh_Map;

	private Players _VatPham_QuyenUuTien;

	private int _VatPham_NoiRotRa;

	public DateTime time
	{
		get
		{
			return _time;
		}
		set
		{
			_time = value;
		}
	}

	public X_Vat_Pham_Loai VatPham
	{
		get
		{
			return _VatPham;
		}
		set
		{
			_VatPham = value;
		}
	}

	public byte[] VatPham_byte
	{
		get
		{
			return _VatPham_byte;
		}
		set
		{
			_VatPham_byte = value;
		}
	}

	public float Rxjh_X
	{
		get
		{
			return _Rxjh_X;
		}
		set
		{
			_Rxjh_X = value;
		}
	}

	public float Rxjh_Y
	{
		get
		{
			return _Rxjh_Y;
		}
		set
		{
			_Rxjh_Y = value;
		}
	}

	public float Rxjh_Z
	{
		get
		{
			return _Rxjh_Z;
		}
		set
		{
			_Rxjh_Z = value;
		}
	}

	public int Rxjh_Map
	{
		get
		{
			return _Rxjh_Map;
		}
		set
		{
			_Rxjh_Map = value;
		}
	}

	public Players VatPham_QuyenUuTien
	{
		get
		{
			return _VatPham_QuyenUuTien;
		}
		set
		{
			_VatPham_QuyenUuTien = value;
		}
	}

	public int VatPham_NoiRotRa
	{
		get
		{
			return _VatPham_NoiRotRa;
		}
		set
		{
			_VatPham_NoiRotRa = value;
		}
	}

	public X_Mat_Dat_Vat_Pham_Loai()
	{
		PlayList = new();
		CurrentZone = ZoneManager.Instance.DefaultZone;
	}

	~X_Mat_Dat_Vat_Pham_Loai()
	{
	}

	public void Dispose()
	{
		try
		{
			if (npcyd != null)
			{
				npcyd.Enabled = false;
				npcyd.Close();
				npcyd.Dispose();
				npcyd = null;
			}
			if (PlayList != null)
			{
				PlayList.Clear();
				PlayList.Dispose();
			}
			PlayList = null;
			VatPham = null;
			VatPham_QuyenUuTien = null;
			if (CurrentZone != null)
			{
				CurrentZone.RemoveItem(this);
				CurrentZone = null;
			}
		}
		catch (Exception)
		{
		}
	}

	public X_Mat_Dat_Vat_Pham_Loai(byte[] VatPham_byte_, float x, float y, float z, int map, Players name, int VatPham_NoiRotRa)
	{
		PlayList = new();
		VatPham_QuyenUuTien = name;
		time = DateTime.Now;
		VatPham = new(VatPham_byte_);
		VatPham_byte = VatPham_byte_;
		id = BitConverter.ToInt64(VatPham.DatDuocGlobal_ID(), 0);
		Rxjh_X = x;
		Rxjh_Y = y;
		Rxjh_Z = z;
		Rxjh_Map = map;
		this.VatPham_NoiRotRa = VatPham_NoiRotRa;
		npcyd = new(60000.0);
		npcyd.Elapsed += VatPhamBienMat;
		npcyd.Enabled = true;
		npcyd.AutoReset = false;
		if (name != null && name.CurrentZone != null)
		{
			CurrentZone = name.CurrentZone;
		}
		else
		{
			CurrentZone = ZoneManager.Instance.DefaultZone;
		}
		CurrentZone?.AddItem(this);
	}

	public void npcydtheout()
	{
		try
		{
			if (npcyd != null)
			{
				npcyd.Enabled = false;
				npcyd.Close();
				npcyd.Dispose();
				npcyd = null;
			}
			World.ItmeTeM.Remove(id);
			GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "VatPhamBienMat errornpcydtheout ：" + BitConverter.ToInt32(VatPham.DatDuocGlobal_ID(), 0) + " [" + VatPham.GetItemName() + "]" + ex);
		}
		finally
		{
			World.ItmeTeM.Remove(id);
			Dispose();
		}
	}

	public void VatPhamBienMat(object sender, ElapsedEventArgs e)
	{
		try
		{
			if (npcyd != null)
			{
				npcyd.Enabled = false;
				npcyd.Close();
				npcyd.Dispose();
				npcyd = null;
			}
			World.ItmeTeM.Remove(id);
			GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "VatPhamBienMat error !! ：" + BitConverter.ToInt64(VatPham.DatDuocGlobal_ID(), 0) + " [" + VatPham.GetItemName() + "]" + ex);
		}
		finally
		{
			World.ItmeTeM.Remove(id);
			Dispose();
		}
	}

	public void GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage()
	{
		try
		{
			foreach (var value in World.allConnectedChars.Values)
			{
				if (FindPlayers(400, value))
				{
					value.ThuThap_VatPham_Drop_PhamVi();
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "GetARangeOfPlayersSendGroundIncreaseItemSoLieuPackage error：" + ex);
		}
	}

	public void GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage()
	{
		try
		{
			if (PlayList == null)
			{
				return;
			}
			try
			{
				foreach (var value in PlayList.Values)
				{
					value.ThuThap_VatPham_Drop_PhamVi();
				}
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage1 error：" + ex);
			}
			PlayList?.Clear();
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "GetARangeOfPlayersSendGroundBienMatVatPhamSoLieuPackage3 error：" + ex2);
		}
	}

	public bool FindPlayers(int far_, Players player)
	{
		if (player.NhanVatToaDo_BanDo != Rxjh_Map)
		{
			return false;
		}
		var num = player.NhanVatToaDo_X - Rxjh_X;
		var num2 = player.NhanVatToaDo_Y - Rxjh_Y;
		bool isInRange = (int)Math.Sqrt((double)num * (double)num + (double)num2 * (double)num2) <= (double)far_;
		if (!isInRange)
		{
			return false;
		}
		return CanPlayerSeeItem(player);
	}

	public bool CanPlayerSeeItem(Players player)
	{
		if (player.CurrentZone == null || CurrentZone == null)
			return true;
		return player.CurrentZone.CanSeeZone(CurrentZone);
	}

	public bool CanPlayerPickupItem(Players player)
	{
		if (player.CurrentZone == null || CurrentZone == null)
			return true;
		if (player.CurrentZone.ID == 0 && CurrentZone.ID == 0)
			return true;
		if (player.CurrentZone == CurrentZone)
			return true;
		return false;
	}

	public void SetZone(Zone zone)
	{
		CurrentZone?.RemoveItem(this);
		CurrentZone = zone;
		CurrentZone?.AddItem(this);
	}

	public static X_Mat_Dat_Vat_Pham_Loai GetItme(long id)
	{
		if (World.ItmeTeM.TryGetValue(id, out var value))
		{
			return value;
		}
		return null;
	}
}
