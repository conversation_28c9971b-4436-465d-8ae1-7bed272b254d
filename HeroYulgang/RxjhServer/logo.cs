using System;
using System.IO;

namespace RxjhServer;

public class logo
{
	public static void Log_Xoa_Ctrl_Alt_ClickChuot(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Xoa_Ctrl_Alt_ClickChuot"))
			{
				Directory.CreateDirectory("logs\\Log_Xoa_Ctrl_Alt_ClickChuot");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Xoa_Ctrl_Alt_ClickChuot\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Dis_Loi_Yes_No_1(string NoiDungLog)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Dis_Loi_Yes_No_1"))
			{
				Directory.CreateDirectory("logs\\Log_Dis_Loi_Yes_No_1");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Dis_Loi_Yes_No_1\\" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", NoiDungLog, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Check_Item_Trung_Seri(string NoiDungLog)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Item_Trung_Seri"))
			{
				Directory.CreateDirectory("logs\\Log_Item_Trung_Seri");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Item_Trung_Seri\\" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", NoiDungLog, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Item_Cat_Kho_Bi_Mat(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Item_Cat_Kho_Bi_Mat"))
			{
				Directory.CreateDirectory("logs\\Log_Item_Cat_Kho_Bi_Mat");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Item_Cat_Kho_Bi_Mat\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_CPVP(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_CPVP"))
			{
				Directory.CreateDirectory("logs\\Log_CPVP");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_CPVP\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Hai_Thuoc(string UserName)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Hai_Thuoc"))
			{
				Directory.CreateDirectory("logs\\Log_Hai_Thuoc");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Hai_Thuoc\\" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", UserName, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_add_Item_TrucTiep_Boss(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_add_ItemTrucTiepBoss"))
			{
				Directory.CreateDirectory("logs\\Log_add_ItemTrucTiepBoss");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_add_ItemTrucTiepBoss\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void log_mua_Tien_Linh_Don(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\log_mua_TienLinhDon"))
			{
				Directory.CreateDirectory("logs\\log_mua_TienLinhDon");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\log_mua_TienLinhDon\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Die_x2_x3_x4_x5_TLC(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Die_x2_x3_x4_x5_TLC"))
			{
				Directory.CreateDirectory("logs\\Log_Die_x2_x3_x4_x5_TLC");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Die_x2_x3_x4_x5_TLC\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Drop_BOSS_loi(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Drop_BOSS_loi"))
			{
				Directory.CreateDirectory("logs\\Log_Drop_BOSS_loi");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Drop_BOSS_loi\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_ADD_ITEM_TOOL(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_ADD_ITEM_TOOL"))
			{
				Directory.CreateDirectory("logs\\Log_ADD_ITEM_TOOL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_ADD_ITEM_TOOL\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Change_Skill(string Txt)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Change_Skill"))
			{
				Directory.CreateDirectory("logs\\Log_Change_Skill");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Change_Skill\\" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Dis_Automatic_AttackEvent(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Dis_Automatic_Attack_Event"))
			{
				Directory.CreateDirectory("Logs\\Log_Dis_Automatic_Attack_Event");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Dis_Automatic_Attack_Event\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Tru_DaiChienHon(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Tru_DaiChienHon"))
			{
				Directory.CreateDirectory("Logs\\Log_Tru_DaiChienHon");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Tru_DaiChienHon\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Kich_Hoat_Tai_Khoan(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Kich_Hoat_Tai_Khoan"))
			{
				Directory.CreateDirectory("Logs\\Log_Kich_Hoat_Tai_Khoan");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Kich_Hoat_Tai_Khoan\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_PhatHienNhipTim(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_PhatHienNhipTim"))
			{
				Directory.CreateDirectory("Logs\\Log_PhatHienNhipTim");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_PhatHienNhipTim\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void logdis(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\log_dis"))
			{
				Directory.CreateDirectory("Logs\\log_dis");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\log_dis\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Gift_Code_Like_Share(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\log_disconnect"))
			{
				Directory.CreateDirectory("Logs\\log_disconnect");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\log_disconnect\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Lag_Dame(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Lag_Dame"))
			{
				Directory.CreateDirectory("Logs\\Log_Lag_Dame");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Lag_Dame\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void CH_ITEM_DKT(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\CH_ITEM_DKT"))
			{
				Directory.CreateDirectory("Logs\\CH_ITEM_DKT");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\CH_ITEM_DKT\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void CH_ITEM_BUA(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\CH_ITEM_BUA"))
			{
				Directory.CreateDirectory("Logs\\CH_ITEM_BUA");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\CH_ITEM_BUA\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Chat(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Chat"))
			{
				Directory.CreateDirectory("logs\\Log_Chat");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Chat\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_HopThanh(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_HopThanh"))
			{
				Directory.CreateDirectory("logs\\LOG_HopThanh");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_HopThanh\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Mat_Item_Boss_Drop(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Mat_Item_Boss_Drop"))
			{
				Directory.CreateDirectory("logs\\Log_Mat_Item_Boss_Drop");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Mat_Item_Boss_Drop\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Drop_Item_Boss(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Drop_Item_Boss"))
			{
				Directory.CreateDirectory("logs\\Log_Drop_Item_Boss");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Drop_Item_Boss\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_HopThanh_HoaLongThach(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_HopThanh_HLT"))
			{
				Directory.CreateDirectory("logs\\LOG_HopThanh_HLT");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_HopThanh_HLT\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void CH_Trang_Suc(string string_0)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\CH_TrangSuc"))
			{
				Directory.CreateDirectory("Logs\\CH_TrangSuc");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\CH_TrangSuc\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void CH_Soul_ThucTinh(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_SoulThucTinh"))
			{
				Directory.CreateDirectory("logs\\Logs_SoulThucTinh");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_SoulThucTinh\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void CH_AO_CHOANG(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\CH_AoChoang"))
			{
				Directory.CreateDirectory("logs\\CH_AoChoang");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\CH_AoChoang\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void CH_Than_Thu(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\CH_Than_Thu"))
			{
				Directory.CreateDirectory("logs\\CH_Than_Thu");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\CH_Than_Thu\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Tien_Hoa_Than_Thu(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_Tien_Hoa_TT"))
			{
				Directory.CreateDirectory("logs\\Logs_Tien_Hoa_TT");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_Tien_Hoa_TT\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_Doi_Ten(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_Doi_Ten"))
			{
				Directory.CreateDirectory("logs\\LOG_Doi_Ten");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_Doi_Ten\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Command_ingame_Lenh_an(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Command_ingame_Lenh_an"))
			{
				Directory.CreateDirectory("Logs\\Log_Command_ingame_Lenh_an");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Command_ingame_Lenh_an\\Log_.txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Bug_Gold_Bat_Thuong(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Bug_Gold_Bat_Thuong"))
			{
				Directory.CreateDirectory("Logs\\Log_Bug_Gold_Bat_Thuong");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Bug_Gold_Bat_Thuong\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Add_Gold_Member(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Add_Gold_Mem"))
			{
				Directory.CreateDirectory("Logs\\Log_Add_Gold_Mem");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Add_Gold_Mem\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Bug_VoHuan_va_TTTP(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Bug_VH_va_TTTP"))
			{
				Directory.CreateDirectory("Logs\\Log_Bug_VH_va_TTTP");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Bug_VH_va_TTTP\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Lenh_Add_Quest(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Lenh_Add_Quest"))
			{
				Directory.CreateDirectory("Logs\\Log_Lenh_Add_Quest");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Lenh_Add_Quest\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Chuyen_Doi_AoChoang(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Chuyen_AC"))
			{
				Directory.CreateDirectory("Logs\\Log_Chuyen_AC");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Chuyen_AC\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_vut_item(string Txt, string username)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Vut_Item"))
			{
				Directory.CreateDirectory("Logs\\Log_Vut_Item");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Vut_Item\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_vut_item_roi_nhat(string Txt, string username)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Vut_Item_Roi_Nhat"))
			{
				Directory.CreateDirectory("Logs\\Log_Vut_Item_Roi_Nhat");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Vut_Item_Roi_Nhat\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Command_ingame(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_CMD_Ingame"))
			{
				Directory.CreateDirectory("Logs\\Log_CMD_Ingame");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_CMD_Ingame\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_FlushAll(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_FlushAll"))
			{
				Directory.CreateDirectory("Logs\\Log_FlushAll");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_FlushAll\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Kick_TheLucChien(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Kick_TheLucChien"))
			{
				Directory.CreateDirectory("Logs\\Log_Kick_TheLucChien");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Kick_TheLucChien\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Dis_10054_10060_222(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Dis_10054_10060_222"))
			{
				Directory.CreateDirectory("Logs\\Log_Dis_10054_10060_222");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Dis_10054_10060_222\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_SK1(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_SK1"))
			{
				Directory.CreateDirectory("Logs\\Log_SK1");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_SK1\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Drop_Nhat_Item_Hiem(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Drop_Nhat_Item_Hiem"))
			{
				Directory.CreateDirectory("Logs\\Log_Drop_Nhat_Item_Hiem");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Drop_Nhat_Item_Hiem\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Treo_Shop(string Txt, string Txt1, string Txt2, string Txt3, double Txt4, int Txt5, string Txt6, int Txt7, string Txt8, int Txt9)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Treo_Shop_Ctrl_Y"))
			{
				Directory.CreateDirectory("Logs\\Log_Treo_Shop_Ctrl_Y");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Treo_Shop_Ctrl_Y\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(new object[2]
			{
				DateTime.Now,
				"-BAN[" + Txt + "][" + Txt1 + "]-MUA[" + Txt2 + "][" + Txt3 + "]-Global_Item[" + Txt4 + "]-Code[" + Txt5 + "]-Name[" + Txt6 + "]-SL[" + Txt7 + "]-ThuocTinh[" + Txt8 + "]-Gia[" + Txt9 + "] \r\n["
			}));
		}
		catch
		{
		}
	}

	public static void Log_Bug_Item_Giao_Dich(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Bug_Item_GD"))
			{
				Directory.CreateDirectory("Logs\\Log_Bug_Item_GD");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Bug_Item_GD\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Bug_Item_Treo_Shop(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Bug_Treo_Shop"))
			{
				Directory.CreateDirectory("Logs\\Log_Bug_Treo_Shop");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Bug_Treo_Shop\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Hack_Speed(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Hack_Speed"))
			{
				Directory.CreateDirectory("Logs\\Log_Hack_Speed");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Hack_Speed\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Lenh_Add_Drop_Item(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Log_Add_Drop"))
			{
				Directory.CreateDirectory("Logs\\Log_Add_Drop");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Log_Add_Drop\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Lenh_BuffPill(string Txt)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Lenh_BuffPill"))
			{
				Directory.CreateDirectory("Logs\\Lenh_BuffPill");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Lenh_BuffPill\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", Txt, "\r\n"));
		}
		catch
		{
		}
	}

	public static void LOG_GD_chuyen_GOLD(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_GD_chuyen_GOLD"))
			{
				Directory.CreateDirectory("logs\\LOG_GD_chuyen_GOLD");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_GD_chuyen_GOLD\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_GD_nhan_GOLD(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_GD_nhan_GOLD"))
			{
				Directory.CreateDirectory("logs\\LOG_GD_nhan_GOLD");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_GD_nhan_GOLD\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_Giao_Dich_Vat_Pham(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Giao_Dich_Vat_Pham"))
			{
				Directory.CreateDirectory("logs\\Giao_Dich_Vat_Pham");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Giao_Dich_Vat_Pham\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_dau_thai_tru_point(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\dau_thai_tru_point"))
			{
				Directory.CreateDirectory("logs\\dau_thai_tru_point");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\dau_thai_tru_point\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_CongHuong_Soi(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_CongHuong_Soi"))
			{
				Directory.CreateDirectory("logs\\Log_CongHuong_Soi");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_CongHuong_Soi\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_hop_thanh_DoThan_Pet_ItemMuiTen_ThanThu(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_DoThan_Pet_ItemMuiTen_ThanThu"))
			{
				Directory.CreateDirectory("logs\\Log_DoThan_Pet_ItemMuiTen_ThanThu");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_DoThan_Pet_ItemMuiTen_ThanThu\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_100m_Gold(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_100m_Gold"))
			{
				Directory.CreateDirectory("logs\\Log_100m_Gold");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_100m_Gold\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_100_point(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_100_point"))
			{
				Directory.CreateDirectory("logs\\Log_100_point");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_100_point\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_1000_point(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_1000_point"))
			{
				Directory.CreateDirectory("logs\\Log_1000_point");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_1000_point\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_10000_point(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_10000_point"))
			{
				Directory.CreateDirectory("logs\\Log_10000_point");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_10000_point\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_Su_Dung_Goi_Ho_Tro(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_Su_Dung_Goi_Ho_Tro"))
			{
				Directory.CreateDirectory("logs\\LOG_Su_Dung_Goi_Ho_Tro");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_Su_Dung_Goi_Ho_Tro\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Goi_Donate(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Goi_Donate"))
			{
				Directory.CreateDirectory("logs\\Log_Goi_Donate");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Goi_Donate\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Packet_Num(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Packet_Num"))
			{
				Directory.CreateDirectory("logs\\Log_Packet_Num");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Packet_Num\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Mo_Hop_OPEN(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Mo_Hop_OPEN"))
			{
				Directory.CreateDirectory("logs\\Log_Mo_Hop_OPEN");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Mo_Hop_OPEN\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Hop_Event(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Mo_Hop_Event"))
			{
				Directory.CreateDirectory("logs\\Log_Mo_Hop_Event");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Mo_Hop_Event\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Pill_check_khi_online_vao_game(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Pill_check_khi_online_vao_game"))
			{
				Directory.CreateDirectory("logs\\Log_Pill_check_khi_online_vao_game");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Pill_check_khi_online_vao_game\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_SuDung_KTTX_Online(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_Su_Dung_KTTX_Online"))
			{
				Directory.CreateDirectory("logs\\LOG_Su_Dung_KTTX_Online");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_Su_Dung_KTTX_Online\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_SuDung_KTTX_Auto_Off(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_Su_Dung_KTTX_Auto_Off"))
			{
				Directory.CreateDirectory("logs\\LOG_Su_Dung_KTTX_Auto_Off");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_Su_Dung_KTTX_Auto_Off\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_Su_Dung_Thuoc(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_Su_Dung_Thuoc"))
			{
				Directory.CreateDirectory("logs\\LOG_Su_Dung_Thuoc");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_Su_Dung_Thuoc\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void LOG_Mat_Pill(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_Mat_Pill"))
			{
				Directory.CreateDirectory("logs\\LOG_Mat_Pill");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_Mat_Pill\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_Hop_Event(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_Hop_Event"))
			{
				Directory.CreateDirectory("logs\\LOG_Hop_Event");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_Hop_Event\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_Hop_Code_Rieng_Event_CoMoThongBao(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Hop_Code_Rieng_CoMoThongBao"))
			{
				Directory.CreateDirectory("Logs\\Hop_Code_Rieng_CoMoThongBao");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Hop_Code_Rieng_CoMoThongBao\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void LOG_Hop_Code_Rieng_Event_KhongMoThongBao(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("Logs"))
			{
				Directory.CreateDirectory("Logs");
			}
			if (!Directory.Exists("Logs\\Hop_Code_Rieng_Event_KhongMoThongBao"))
			{
				Directory.CreateDirectory("Logs\\Hop_Code_Rieng_Event_KhongMoThongBao");
			}
			using StreamWriter streamWriter = new(new FileStream("Logs\\Hop_Code_Rieng_Event_KhongMoThongBao\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(string.Concat(DateTime.Now, " ", string_0, "\r\n"));
		}
		catch
		{
		}
	}

	public static void Log_Add_Item_GM(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Add_Item_GM"))
			{
				Directory.CreateDirectory("logs\\Log_Add_Item_GM");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Add_Item_GM\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Add_Item_Lenh_An_trong_Source(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Add_Item_Lenh_An_trong_Source"))
			{
				Directory.CreateDirectory("logs\\Log_Add_Item_Lenh_An_trong_Source");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Add_Item_Lenh_An_trong_Source\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Add_Item_Member(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Add_Item_Member"))
			{
				Directory.CreateDirectory("logs\\Log_Add_Item_Member");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Add_Item_Member\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Lenh_Xoa_Item(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Lenh_Xoa_Item"))
			{
				Directory.CreateDirectory("logs\\Lenh_Xoa_Item");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Lenh_Xoa_Item\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Mua_NPC(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Mua_NPC"))
			{
				Directory.CreateDirectory("logs\\Log_Mua_NPC");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Mua_NPC\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void Log_Ban_NPC(string string_0, string username)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Log_Ban_NPC"))
			{
				Directory.CreateDirectory("logs\\Log_Ban_NPC");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log_Ban_NPC\\" + username + "_Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void LOG_SuDung_CanhBoss(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\LOG_SuDung_CanhBoss"))
			{
				Directory.CreateDirectory("logs\\LOG_SuDung_CanhBoss");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\LOG_SuDung_CanhBoss\\Log5_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void shopTxtLog(string string_0)
	{
		try
		{
			var text = "logs";
			if (!Directory.Exists(text))
			{
				Directory.CreateDirectory(text);
			}
			var path = Path.Combine(text, "ShopLog");
			if (!Directory.Exists(path))
			{
				Directory.CreateDirectory(path);
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\ShopLog\\Log_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void thuoctinherrorlog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Log1_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void kickid(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log2_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void UseItemLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\UseItem"))
			{
				Directory.CreateDirectory("logs\\UseItem");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\UseItem\\Log3_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void HCItemLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log4_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void WGTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log5_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log6_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void RegLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log7.txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void pzTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log8_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void cfzTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log9_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void zhtfTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log10" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileCQTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log11_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileLoninTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log12_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileDropItmeTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log13_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileItmeTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log14_" + DateTime.Now.ToString("yyyy_MM_dd") + ".log", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void FileBugTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log15_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void FilePakTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log16_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void SeveTxtLog(string string_0)
	{
		try
		{
			if (!Directory.Exists("logs"))
			{
				Directory.CreateDirectory("logs");
			}
			if (!Directory.Exists("logs\\Logs_ALL"))
			{
				Directory.CreateDirectory("logs\\Logs_ALL");
			}
			using StreamWriter streamWriter = new(new FileStream("logs\\Logs_ALL\\Log17_" + DateTime.Now.ToString("yyyy_MM_dd") + ".txt", FileMode.Append, FileAccess.Write, FileShare.Read));
			streamWriter.Write(DateTime.Now + " " + string_0 + "\r\n");
		}
		catch
		{
		}
	}

	public static void MsGLog(string string_0)
	{
		try
		{
		}
		catch
		{
		}
	}
}
