using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using HeroYulgang.Helpers;
using RxjhServer.Network;

namespace RxjhServer
{
    /// <summary>
    /// Phần mở rộng của lớp NpcClass để xử lý broadcast trong Zone liên server
    /// </summary>
    public partial class NpcClass
    {
        /// <summary>
        /// Ghi đè phương thức SendCurrentRangeBroadcastData để hỗ trợ broadcast trong Zone liên server
        /// </summary>
        /// <param name="pak">Gói tin cần broadcast</param>
        /// <param name="id">ID của gói tin</param>
        /// <param name="wordid">World ID</param>
        public void SendCurrentRangeBroadcastDataCrossServer(SendingClass pak, int id = 0, int wordid = 0)
        {
            try
            {
                // Kiểm tra xem NPC có đang ở trong zone liên server không
                if (this.CurrentZone == null || !this.CurrentZone.IsCrossServer)
                {
                    // Nếu không phải zone liên server, sử dụng phương thức gốc
                    this.SendCurrentRangeBroadcastData(pak, id, wordid);
                    return;
                }

                // Nếu là zone liên server, kiểm tra xem có phải server gốc không
                //if (this.CurrentZone.OriginServerID == World.ServerID)
                //{
                //    // Nếu là server gốc, broadcast bình thường
                //    this.SendCurrentRangeBroadcastData(pak, id, wordid);
                //}

                // Chuyển đổi pak thành hex string để gửi qua transmit
                string hexData = BitConverter.ToString(pak.ToArray3()).Replace("-", "");

                // Không cần phân loại dữ liệu, chỉ cần gửi trực tiếp
                string dataType = "DATA";

                // Gửi thông tin broadcast đến LoginServer
                World.conn.SendCrossServerNpcBroadcast(
                    this.CurrentZone.ID,
                    this.ID,
                    dataType,
                    hexData,
                    id,
                    wordid
                );
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"SendCurrentRangeBroadcastDataCrossServer error: {ex.Message}");
            }
        }

        public void SendCurrentRangeBroadcastDataCrossServer(byte[] data, int length)
        {
            try
            {
                //Kiểm tra xem NPC có đang ở trong zone liên server không
                if (this.CurrentZone == null || !this.CurrentZone.IsCrossServer)
                {
                    // Nếu không phải zone liên server, sử dụng phương thức gốc
                    this.QuangBaSoLieu(data,length);
                    return;
                }

                // Nếu là zone liên server, kiểm tra xem có phải server gốc không
                //if (this.CurrentZone.OriginServerID == World.ServerID)
                //{
                //    // Nếu là server gốc, broadcast bình thường
                //    this.SendCurrentRangeBroadcastData(pak, id, wordid);
                //}

                // Chuyển đổi pak thành hex string để gửi qua transmit
                 string hexData = BitConverter.ToString(data).Replace("-", "");

                // Không cần phân loại dữ liệu, chỉ cần gửi trực tiếp
                string dataType = "DATA";

                // Gửi thông tin broadcast đến LoginServer
                World.conn.SendCrossServerNpcBroadcast(
                    this.CurrentZone.ID,
                    this.ID,
                    dataType,
                    hexData
                );
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"SendCurrentRangeBroadcastDataCrossServer error: {ex.Message}");
            }
        }


    }
}
