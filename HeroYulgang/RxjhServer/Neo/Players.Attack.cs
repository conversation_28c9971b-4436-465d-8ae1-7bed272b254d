using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using HeroYulgang.Helpers;

namespace RxjhServer
{
    /// <summary>
    /// Phần mở rộng của lớp Players để xử lý tấn công
    /// </summary>
    public partial class Players
    {
        /// <summary>
        /// Hook vào phương thức tấn công người chơi
        /// </summary>
        /// <param name="target">Người chơi bị tấn công</param>
        /// <param name="damage">S<PERSON><PERSON> thương</param>
        /// <returns>true nếu tấn công thành công, false nếu không thành công</returns>
        public bool HookPlayerAttack(Players target, int damage)
        {
            try
            {
                // Gọi hook tấn công để xử lý Zone liên server
                HookAttack(target, damage);
                
                // Kiểm tra xem có thể tấn công không
                if (!Hooks_PlayerMove.CanAttackPlayer(this, target))
                {
                    this.HeThongNhacNho("Bạn không thể tấn công người chơi này");
                    return false;
                }
                
                // Lưu thông tin người chơi đã tấn công
                target.SetLastAttacker(this);
                
                // Thêm người chơi vào danh sách đóng góp
                target.AddContributors(this);
                
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"HookPlayerAttack error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Hook vào phương thức tấn công NPC
        /// </summary>
        /// <param name="target">NPC bị tấn công</param>
        /// <param name="damage">Sát thương</param>
        /// <returns>true nếu tấn công thành công, false nếu không thành công</returns>
        public bool HookNpcAttack(NpcClass target, int damage)
        {
            try
            {
                // Gọi hook tấn công để xử lý Zone liên server
                HookAttack(target, damage);
                
                // Kiểm tra xem có thể tấn công không
                if (!Hooks_PlayerMove.CanAttackNpc(this, target))
                {
                    this.HeThongNhacNho("Bạn không thể tấn công NPC này");
                    return false;
                }
                
                return true;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, $"HookNpcAttack error: {ex.Message}");
                return false;
            }
        }
    }
}
