using System;
using HeroYulgang.Core.Network;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using RxjhServer.Network;

namespace RxjhServer
{
    public partial class Players
    {
        /// <summary>
        /// <PERSON>y<PERSON><PERSON> đổi từ NetState sang ActorNetState
        /// </summary>
        /// <param name="actorNetState"><PERSON><PERSON><PERSON> tượng ActorNetState mới</param>
        public new void MigrateToActorNetState(ActorNetState actorNetState)
        {
            try
            {
                // Cập nhật tham chiếu từ ActorNetState đến Players
                actorNetState.Player = this;

                // Cập nhật property Client
                Client = actorNetState;

                LogHelper.WriteLine(LogLevel.Info, $"Đã chuyển đổi thành công người chơi {UserName} sang ActorNetState");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi chuyển đổi sang ActorNetState: {ex.Message}");
                throw;
            }
        }
    }
}
