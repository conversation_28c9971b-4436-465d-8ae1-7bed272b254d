using System;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Di_Thuong_Trang_Thai_Cong_Kich_Loai : IDisposable
{
	public System.Timers.Timer NhanVatTrangThai;

	public DateTime time;

	public Players Play;

	private int _FLD_PID;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-Dispose");
		}
		if (NhanVatTrangThai != null)
		{
			NhanVatTrangThai.Enabled = false;
			NhanVatTrangThai.Close();
			NhanVatTrangThai.Dispose();
			NhanVatTrangThai = null;
		}
		Play = null;
	}

	public X_Di_Thuong_Trang_Thai_Cong_Kich_Loai(Players Play_, int ThoiGian, int DiThuong_ID, int DiThuong_SoLuong)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-NEW");
		}
		FLD_PID = DiThuong_ID;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Play = Play_;
		NhanVatTrangThai = new(ThoiGian);
		NhanVatTrangThai.Elapsed += ThoiGianKetThucSuKien1;
		NhanVatTrangThai.Enabled = true;
		NhanVatTrangThai.AutoReset = false;
		StatusEffect(FLD_PID, 1, DiThuong_SoLuong, ThoiGian / 1000);
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "ThoiGianKetThucSuKien1");
		}
		ThoiGianKetThucSuKien();
	}

	public void ThoiGianKetThucSuKien()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-ThoiGianKetThucSuKien");
		}
		try
		{
			if (Play != null)
			{
				if (FLD_PID == 1)
				{
					Play.FLD_DuocPham_GiamBotCongKich += 0.2;
					Play.UpdateMartialArtsAndStatus();
				}
				if (FLD_PID == 13)
				{
					Play.FLD_DuocPham_GiamBotCongKich += 0.1;
					Play.UpdateMartialArtsAndStatus();
				}
				if (Play.TrangThai_TanCong_BatThuong != null)
				{
					Play.TrangThai_TanCong_BatThuong.Remove(FLD_PID);
				}
				StatusEffect(FLD_PID, 0, 0, 0);
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "TrangThai_BatThuongClass ThoiGianKetThucSuKien error 11 ：[" + FLD_PID + "]" + ex);
		}
		finally
		{
			Dispose();
		}
	}

	public void StatusEffect(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
	{
		var array = Converter.HexStringToByte("AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 58, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 22, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 62, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(ThoiGian), 0, array, 38, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_SoLuong), 0, array, 42, 4);
		if (Play != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.SendMultiplePackage(array, array.Length);
			}
			Play.Send_Nhieu_Packet_PhamVi_HienTai(array, array.Length);
		}
	}
}
