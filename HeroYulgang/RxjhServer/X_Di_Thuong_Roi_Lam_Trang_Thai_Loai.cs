using System;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_Di_Thuong_Roi_Lam_Trang_Thai_Loai : IDisposable
{
	public System.Timers.Timer QuaiVat_TrangThai;

	public System.Timers.Timer DiThuongRoiLam_ThoiGian;

	public System.Timers.Timer NhanVatTrangThai;

	public double ycztsl;

	public DateTime time;

	public Players Play;

	public NpcClass Npc;

	public int NpcPlayId;

	private int _FLD_PID;

	public int FLD_PID
	{
		get
		{
			return _FLD_PID;
		}
		set
		{
			_FLD_PID = value;
		}
	}

	public void Dispose()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_BatThuongClass-Dispose");
		}
		if (QuaiVat_TrangThai != null)
		{
			QuaiVat_TrangThai.Enabled = false;
			QuaiVat_TrangThai.Close();
			QuaiVat_TrangThai.Dispose();
			QuaiVat_TrangThai = null;
		}
		if (NhanVatTrangThai != null)
		{
			NhanVatTrangThai.Enabled = false;
			NhanVatTrangThai.Close();
			NhanVatTrangThai.Dispose();
			NhanVatTrangThai = null;
		}
		if (DiThuongRoiLam_ThoiGian != null)
		{
			DiThuongRoiLam_ThoiGian.Enabled = false;
			DiThuongRoiLam_ThoiGian.Close();
			DiThuongRoiLam_ThoiGian.Dispose();
			DiThuongRoiLam_ThoiGian = null;
		}
		Play = null;
		Npc = null;
	}

	public X_Di_Thuong_Roi_Lam_Trang_Thai_Loai(Players Play_, int ThoiGian, int DiThuong_ID, int DiThuong_SoLuong)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_XanhLam_BatThuong tốt bụng-NEW");
		}
		FLD_PID = DiThuong_ID;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Play = Play_;
		NhanVatTrangThai = new(ThoiGian);
		NhanVatTrangThai.Elapsed += ThoiGianKetThucSuKien1;
		NhanVatTrangThai.Enabled = true;
		NhanVatTrangThai.AutoReset = false;
		StatusEffect(FLD_PID, 1, DiThuong_SoLuong, ThoiGian / 1000);
	}

	public void TrangThai_BatThuongLoai_RoiLam(double ycztsll)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_XanhLam_BatThuong tốt bụng-TrangThai_XanhLam_BatThuong Giống như xuất huyết");
		}
		ycztsl = ycztsll;
		DiThuongRoiLam_ThoiGian = new(1000.0);
		DiThuongRoiLam_ThoiGian.Elapsed += AbnormalBlueEvent;
		DiThuongRoiLam_ThoiGian.Enabled = true;
		DiThuongRoiLam_ThoiGian.AutoReset = true;
	}

	public void AbnormalBlueEvent(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "Abnormal Blue Event");
		}
		if (Play != null)
		{
			Play.NhanVat_MP -= (int)ycztsl;
			if (Play.NhanVat_MP <= 0 && DiThuongRoiLam_ThoiGian != null)
			{
				DiThuongRoiLam_ThoiGian.Enabled = false;
				DiThuongRoiLam_ThoiGian.Close();
				DiThuongRoiLam_ThoiGian.Dispose();
				DiThuongRoiLam_ThoiGian = null;
			}
			Play.CapNhat_HP_MP_SP();
		}
	}

	public X_Di_Thuong_Roi_Lam_Trang_Thai_Loai(NpcClass Play_, int _NpcPlayId, int ThoiGian, int DiThuong_ID, int DiThuong_SoLuong)
	{
		NpcPlayId = _NpcPlayId;
		FLD_PID = DiThuong_ID;
		time = DateTime.Now;
		time = time.AddMilliseconds(ThoiGian);
		Npc = Play_;
		QuaiVat_TrangThai = new(ThoiGian);
		QuaiVat_TrangThai.Elapsed += ThoiGianKetThucSuKien1;
		QuaiVat_TrangThai.Enabled = true;
		QuaiVat_TrangThai.AutoReset = false;
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "ThoiGianKetThucSuKien1");
		}
		ThoiGianKetThucSuKien();
	}

	public void ThoiGianKetThucSuKien()
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai BatThuongClass ThoiGianKet ThucSuKien");
		}
		try
		{
			if (QuaiVat_TrangThai != null)
			{
				QuaiVat_TrangThai.Enabled = false;
				QuaiVat_TrangThai.Close();
				QuaiVat_TrangThai.Dispose();
				QuaiVat_TrangThai = null;
			}
			if (Npc != null)
			{
				if (Npc.TrangThai_BatThuong != null)
				{
					Npc.TrangThai_BatThuong.Remove(FLD_PID);
				}
				Dispose();
			}
			else if (Play != null)
			{
				if (Play.TrangThai_XanhLam_BatThuong != null)
				{
					Play.TrangThai_XanhLam_BatThuong.Remove(FLD_PID);
				}
				StatusEffect(FLD_PID, 0, 0, 0);
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "TrangThai_XanhLam_BatThuong tốt bụng ThoiGianKetThucSuKien error：[" + FLD_PID + "]" + ex);
		}
		finally
		{
			Dispose();
		}
	}

	public void StatusEffect(int DiThuong_ID, int SwitchOnOff, int DiThuong_SoLuong, int ThoiGian)
	{
		if (World.jlMsg == 1)
		{
			LogHelper.WriteLine(0, "TrangThai_XanhLam_BatThuong tốt bụng-TrangThaiHieuQua");
		}
		var array = Converter.HexStringToByte("AA5546003527401538008C0300002C0100000900000001000000000000006016A2496016A2492600000014000000000000008C030000E80300000900000001000000000000000000000055AA");
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 18, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_ID), 0, array, 58, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 22, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(SwitchOnOff), 0, array, 62, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(ThoiGian), 0, array, 38, 4);
		System.Buffer.BlockCopy(BitConverter.GetBytes(DiThuong_SoLuong), 0, array, 42, 4);
		if (Play != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.SendMultiplePackage(array, array.Length);
			}
			Play.Send_Nhieu_Packet_PhamVi_HienTai(array, array.Length);
		}
		else if (Npc != null)
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.NPC_SessionID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Npc.NPC_SessionID), 0, array, 4, 2);
			Npc.QuangBaSoLieu(array, array.Length);
		}
	}
}
