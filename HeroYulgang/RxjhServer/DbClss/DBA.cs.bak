using System;
using System.Collections;
using System.Data;
using System.Data.SqlClient;

namespace RxjhServer.DbClss;

public class DBA
{
	public static void serlog(string string_0)
	{
		var sqlJl = World.SqlJl;
		if (sqlJl.Length == 0)
		{
			return;
		}
		var text = sqlJl;
		var separator = new char[1] { '|' };
		var array = text.Split(separator);
		var array2 = array;
		foreach (var text2 in array2)
		{
			if (string_0.ToLower().IndexOf(text2.ToLower()) != -1)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, string_0);
			}
		}
	}

	public static void serlog(string string_0, SqlParameter[] sqlParameter_0)
	{
		var sqlJl = World.SqlJl;
		if (sqlJl.Length == 0)
		{
			return;
		}
		var array = sqlJl.Split('|');
		for (var i = 0; i < array.Length; i++)
		{
			if (string_0.ToLower().IndexOf(array[i].ToLower()) != -1)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, string_0);
			}
		}
		for (var j = 0; j < array.Length; j++)
		{
			foreach (var sqlParameter in sqlParameter_0)
			{
				if (sqlParameter.SqlValue.ToString().ToLower().IndexOf(array[j].ToLower()) != -1)
				{
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Debug, string_0 + " " + sqlParameter.SqlValue.ToString());
				}
			}
		}
	}

	public static void Setlog(string string_0, SqlParameter[] sqlParameter_0, Exception exception_0)
	{
		LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DBASoLieu Lỗi lớp "+exception_0.Message);
		LogHelper.WriteLine(100, string_0);
		if (sqlParameter_0 != null)
		{
			foreach (var sqlParameter in sqlParameter_0)
			{
				LogHelper.WriteLine(100, sqlParameter.SqlValue.ToString());
			}
		}
		LogHelper.WriteLine(100, exception_0.Message);
	}

	public static string getstrConnection(string string_0)
	{
		try
		{
			string_0 ??= "GameServer";
            return World.Db.TryGetValue(string_0, out DbClass value) ? value.SqlConnect : null;
        }
		catch
		{
			return null;
		}
	}

	public static int ExeSqlCommand(string string_0, SqlParameter[] sqlParameter_0)
	{
		serlog(string_0, sqlParameter_0);
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using var sqlCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0);
		var result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return -1;
		}
		try
		{
			result = sqlCommand.ExecuteNonQuery();
		}
		catch (Exception exception_)
		{
			Setlog(string_0, sqlParameter_0, exception_);
		}
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}
	public static int ExeSqlCommand(string query, string connectionName, SqlParameter[] parameters = null)
    {
        serlog(query);
        using SqlConnection sqlConnection = new(getstrConnection(connectionName));
        using SqlCommand sqlCommand = new(query + "; SELECT SCOPE_IDENTITY();", sqlConnection);
        var result = -1;

        try
        {
            sqlConnection.Open();

            // Add parameters if they exist
            if (parameters != null) sqlCommand.Parameters.AddRange(parameters);

            // Execute the command and get the inserted ID
            var id = sqlCommand.ExecuteScalar();
            result = id != null ? Convert.ToInt32(id) : -1;
        }
        catch (Exception exception_)
        {
            Setlog(query, parameters, exception_);
        }
        finally
        {
            sqlCommand.Dispose();
            sqlConnection.Close();
            sqlConnection.Dispose();
        }

        return result;
    }

	public static int ExeSqlCommand(string string_0, SqlParameter[] sqlParameter_0, string string_1)
	{
		serlog(string_0, sqlParameter_0);
		using SqlConnection sqlConnection = new(getstrConnection(string_1));
		using var sqlCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0);
		var result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return -1;
		}
		try
		{
			result = sqlCommand.ExecuteNonQuery();
		}
		catch (Exception exception_)
		{
			Setlog(string_0, sqlParameter_0, exception_);
		}
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static int ExeSqlCommand(string string_0)
	{
		serlog(string_0);
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using SqlCommand sqlCommand = new(string_0, sqlConnection);
		var result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return -1;
		}
		try
		{
			result = sqlCommand.ExecuteNonQuery();
		}
		catch (Exception exception_)
		{
			Setlog(string_0, null, exception_);
		}
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static int ExeSqlCommand(string string_0, string string_1)
	{
		serlog(string_0);
		using SqlConnection sqlConnection = new(getstrConnection(string_1));
		using SqlCommand sqlCommand = new(string_0, sqlConnection);
		var result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return -1;
		}
		try
		{
			result = sqlCommand.ExecuteNonQuery();
		}
		catch (Exception exception_)
		{
			Setlog(string_0, null, exception_);
		}
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static int ExeSqlCommand2(string string_0, string string_1)
	{
		serlog(string_0);
		using SqlConnection sqlConnection = new(getstrConnection(string_1));
		using SqlCommand sqlCommand = new(string_0, sqlConnection);
		var result = -1;
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return -1;
		}
		try
		{
			result = (int)sqlCommand.ExecuteScalar();
		}
		catch (Exception exception_)
		{
			Setlog(string_0, null, exception_);
		}
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static int ExeSqlCommand(string string_0, ref Exception exception_0, string string_1)
	{
		serlog(string_0);
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using SqlCommand sqlCommand = new(string_0, sqlConnection);
		try
		{
			sqlConnection.Open();
		}
		catch (Exception ex)
		{
			var ex2 = (exception_0 = ex);
			return -1;
		}
		var result = sqlCommand.ExecuteNonQuery();
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	public static DataTable GetDBToDataTable(string string_0, SqlParameter[] sqlParameter_0)
	{
		serlog(string_0, sqlParameter_0);
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using SqlDataAdapter sqlDataAdapter = new();
		var sqlCommand2 = (sqlDataAdapter.SelectCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0));
		var sqlCommand3 = sqlCommand2;
		using (sqlCommand3)
		{
			try
			{
				sqlConnection.Open();
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DBASoLieu Lỗi lớp 11 " + ex.Message + " " + string_0);
				return null;
			}
			DataTable dataTable = new();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception exception_)
			{
				Setlog(string_0, sqlParameter_0, exception_);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static DataTable GetDBToDataTable(string string_0, SqlParameter[] sqlParameter_0, string string_1)
	{
		serlog(string_0, sqlParameter_0);
		using SqlConnection sqlConnection = new(getstrConnection(string_1));
		using SqlDataAdapter sqlDataAdapter = new();
		var sqlCommand2 = (sqlDataAdapter.SelectCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0));
		var sqlCommand3 = sqlCommand2;
		using (sqlCommand3)
		{
			try
			{
				sqlConnection.Open();
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DBASoLieu Lỗi lớp 22 " + ex.Message + " " + string_0);
				return null;
			}
			DataTable dataTable = new();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception exception_)
			{
				Setlog(string_0, sqlParameter_0, exception_);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static DataTable GetDBToDataTable(string string_0)
	{
		serlog(string_0);
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using SqlDataAdapter sqlDataAdapter = new();
		var sqlCommand2 = (sqlDataAdapter.SelectCommand = new(string_0, sqlConnection));
		var sqlCommand3 = sqlCommand2;
		using (sqlCommand3)
		{
			try
			{
				sqlConnection.Open();
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DBASoLieu Lỗi lớp 33 " + ex.Message + " " + string_0);
				return null;
			}
			DataTable dataTable = new();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception exception_)
			{
				Setlog(string_0, null, exception_);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static DataTable GetDBToDataTable(string string_0, string string_1)
	{
		serlog(string_0);
		using SqlConnection sqlConnection = new(getstrConnection(string_1));
		using SqlDataAdapter sqlDataAdapter = new();
		var sqlCommand2 = (sqlDataAdapter.SelectCommand = new(string_0, sqlConnection));
		var sqlCommand3 = sqlCommand2;
		using (sqlCommand3)
		{
			try
			{
				sqlConnection.Open();
			}
			catch
			{
				return null;
			}
			DataTable dataTable = new();
			try
			{
				sqlDataAdapter.Fill(dataTable);
			}
			catch (Exception exception_)
			{
				Setlog(string_0, null, exception_);
			}
			sqlDataAdapter.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return dataTable;
		}
	}

	public static DataRowCollection GetDBValue(string string_0, string string_1)
	{
		return GetDBToDataTable(string_0).Rows;
	}

	public static ArrayList GetDBValue_1(string string_0, string string_1)
	{
		serlog(string_0);
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using SqlCommand sqlCommand = new(string_0, sqlConnection);
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return null;
		}
		var sqlDataReader = sqlCommand.ExecuteReader();
		if (!sqlDataReader.HasRows)
		{
			sqlDataReader.Close();
			sqlDataReader.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return null;
		}
		ArrayList arrayList = new();
		if (sqlDataReader.Read())
		{
			for (var i = 0; i < sqlDataReader.FieldCount; i++)
			{
				arrayList.Add(sqlDataReader[i]);
			}
		}
		sqlDataReader.Close();
		sqlDataReader.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		sqlCommand.Dispose();
		return arrayList;
	}

	public static ArrayList GetDBValue_2(string string_0, string string_1)
	{
		serlog(string_0);
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using SqlCommand sqlCommand = new(string_0, sqlConnection);
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return null;
		}
		var sqlDataReader = sqlCommand.ExecuteReader();
		if (!sqlDataReader.HasRows)
		{
			sqlDataReader.Close();
			sqlDataReader.Dispose();
			sqlConnection.Close();
			sqlConnection.Dispose();
			return null;
		}
		ArrayList arrayList = new();
		while (sqlDataReader.Read())
		{
			arrayList.Add(sqlDataReader[0]);
		}
		sqlDataReader.Close();
		sqlDataReader.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		sqlCommand.Dispose();
		return arrayList;
	}

	public static object GetDBValue_3(string string_0)
	{
		serlog(string_0);
		object result = null;
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using SqlCommand sqlCommand = new(string_0, sqlConnection);
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return null;
		}
		try
		{
			result = sqlCommand.ExecuteScalar();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DBASoLieu Lỗi lớp 44 " + ex.Message + " " + string_0);
		}
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}
	public static object GetDBValue_3(string string_0, string string_1)
        {
            serlog(string_0);
            object result = null;
            using SqlConnection sqlConnection = new(getstrConnection(string_1));
            using SqlCommand sqlCommand = new(string_0, sqlConnection);
            try
            {
                sqlConnection.Open();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(100, "DBASoLieu层_错误" + ex.Message + " " + string_0);
                return null;
            }
    
            try
            {
                result = sqlCommand.ExecuteScalar();
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(100, "DBASoLieu层_错误" + ex.Message + " " + string_0);
            }
    
            sqlCommand.Dispose();
            sqlConnection.Close();
            sqlConnection.Dispose();
            return result;
        }

	public static object GetDBValue_3(string string_0, SqlParameter[] sqlParameter_0)
	{
		serlog(string_0, sqlParameter_0);
		object result = null;
		using SqlConnection sqlConnection = new(getstrConnection(null));
		using var sqlCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0);
		try
		{
			sqlConnection.Open();
		}
		catch
		{
			return null;
		}
		try
		{
			result = sqlCommand.ExecuteScalar();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "DBASoLieu Lỗi lớp 55 " + ex.Message + " " + string_0);
		}
		sqlCommand.Dispose();
		sqlConnection.Close();
		sqlConnection.Dispose();
		return result;
	}

	
	public static object GetDBValue_3(string string_0, SqlParameter[] sqlParameter_0, string string_1)
    {
        serlog(string_0, sqlParameter_0);
        object result = null;
        using SqlConnection sqlConnection = new(getstrConnection(string_1));
        using var sqlCommand = SqlDBA.CreateCommandSql(sqlConnection, string_0, sqlParameter_0);
        try
        {
            sqlConnection.Open();
        }
        catch
        {
            return null;
        }

        try
        {
            result = sqlCommand.ExecuteScalar();
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(100, "DBASoLieu层_错误15" + ex.Message + " " + string_0);
        }

        sqlCommand.Dispose();
        sqlConnection.Close();
        sqlConnection.Dispose();
        return result;
    }
}
