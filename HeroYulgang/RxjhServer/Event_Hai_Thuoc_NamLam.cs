using System;
using System.Collections.Generic;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class Event_Hai_Thuoc_NamLam
{
	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private DateTime dateTime_0;

	private DateTime iqOqpBruKS;

	public Event_Hai_Thuoc_NamLam()
	{
		try
		{
			World.Event_HaiThuoc_Progress = 1;
			World.Npc_HaiThuoc_NamLam.Clear();
			dateTime_0 = DateTime.Now.AddMinutes(5.0);
			ThoiGian1 = new(3000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				Call_Item_HaiThuoc_NamLam();
				{
					foreach (var value in World.allConnectedChars.Values)
					{
						if (!value.Client.TreoMay)
						{
							value.HeThongNhacNho("Sự kiện hái thuốc đổi võ huân khai mở, đại hiệp có [" + num / 60 + "] khắc để thu hoạch linh dược!", 10, "Thiên cơ các");
							GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num);
						}
					}
					return;
				}
			}
			ThoiGianKetThucSuKien1(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BOSS Event Hai Thuoc Progress = 1 lỗi !! ----------" + ex.Message);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)dateTime_0.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			World.Event_HaiThuoc_Progress = 2;
			iqOqpBruKS = DateTime.Now.AddMinutes(10.0);
			ThoiGian2 = new(3000.0);
			ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
			ThoiGian2.Enabled = true;
			ThoiGian2.AutoReset = true;
			var num2 = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num2 <= 0)
			{
				return;
			}
			foreach (var value in World.allConnectedChars.Values)
			{
				if (!value.Client.TreoMay)
				{
					GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(value, num2);
					value.HeThongNhacNho("Nhiệm vụ khép lại sau [" + num2 / 60 + "] khắc, hết giờ đại hiệp không nhận được bảo vật!", 10, "Thiên cơ các");
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Event hái thuốc 222 Phạm sai lầm：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)iqOqpBruKS.Subtract(DateTime.Now).TotalSeconds;
			if (num > 0)
			{
				return;
			}
			using (var enumerator = World.allConnectedChars.Values.GetEnumerator())
			{
				if (enumerator.MoveNext())
				{
					var current = enumerator.Current;
					if (!current.Client.TreoMay)
					{
						current.HeThongNhacNho("Sự kiện Hái Thuốc tại cổng thành Huyền Bột hôm nay khép lại, chúc quần hùng vui vẻ!", 10, "Thiên cơ các");
						World.SystemRollingAnnouncement("Sự kiện Hái Thuốc tại cổng thành Huyền Bột đã kết thúc. Chúc các bạn vui vẻ!!");
					}
				}
			}
			World.Event_HaiThuoc_Progress = 0;
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
			World.HaiThuoc_NamLam_Event.Dispose();
			Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Tính toán Hái Thuốc Event còn thừa số lượng phạm sai lầm：" + ex);
		}
	}

	public static void GUI_DI_THE_LUC_CHIEN_BAT_DAU_DEM_NGUOC(Players player, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101000000000000000000000000000000000000000002EE55AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			if (player.Client != null)
			{
				player.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void Call_Item_HaiThuoc_NamLam()
	{
		try
		{
			AddNpc_SoLuong(15281, 394f, 1181f, 101, 20);
			AddNpc_SoLuong(15281, 162f, 1181f, 101, 20);
			AddNpc_SoLuong(15281, 282f, 1181f, 101, 20);
			AddNpc_SoLuong(15282, 378f, 1211f, 101, 20);
			AddNpc_SoLuong(15282, 367f, 1115f, 101, 20);
			AddNpc_SoLuong(15282, 250f, 1115f, 101, 20);
			AddNpc_SoLuong(15283, 146f, 1118f, 101, 20);
			AddNpc_SoLuong(15283, 168f, 1211f, 101, 20);
			AddNpc_SoLuong(15283, 297f, 1212f, 101, 20);
			AddNpc_SoLuong(15281, 549f, 1267f, 101, 20);
			AddNpc_SoLuong(15282, 557f, 1144f, 101, 20);
			AddNpc_SoLuong(15283, 560f, 1208f, 101, 20);
			AddNpc_SoLuong(15283, 529f, 1183f, 101, 20);
		}
		catch
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "BOSS Event Noel lỗi !! ----------");
		}
	}

	public void AddNpc_SoLuong(int int_0, float float_0, float float_1, int int_1, int soluong)
	{
		try
		{
			for (var i = 0; i < soluong; i++)
			{
				var num = RNG.Next((int)float_0 - 100, (int)float_0 + 100);
				var num2 = RNG.Next((int)float_1 - 100, (int)float_1 + 100);
				if (World.MonsterList.TryGetValue(int_0, out var value))
				{
					NpcClass npcClass = new();
					npcClass.FLD_PID = value.FLD_PID;
					npcClass.Name = value.Name;
					npcClass.Level = value.Level;
					npcClass.Rxjh_Exp = value.Rxjh_Exp;
					npcClass.Rxjh_X = num;
					npcClass.Rxjh_Y = num2;
					npcClass.Rxjh_Z = 15f;
					npcClass.Rxjh_cs_X = num;
					npcClass.Rxjh_cs_Y = num2;
					npcClass.Rxjh_cs_Z = 15f;
					npcClass.Rxjh_Map = int_1;
					npcClass.IsNpc = 0;
					npcClass.FLD_FACE1 = RNG.Next(-1, 1);
					npcClass.FLD_FACE2 = RNG.Next(-1, 1);
					npcClass.Max_Rxjh_HP = value.Rxjh_HP;
					npcClass.Rxjh_HP = value.Rxjh_HP;
					npcClass.FLD_AT = value.FLD_AT;
					npcClass.FLD_DF = value.FLD_DF;
					npcClass.FLD_AUTO = value.FLD_AUTO;
					npcClass.FLD_BOSS = 0;
					npcClass.FLD_NEWTIME = 2;
					npcClass.QuaiXuatHien_DuyNhatMotLan = false;
					npcClass.timeNpc_HoiSinh = DateTime.MinValue;
					if (World.Map.TryGetValue(npcClass.Rxjh_Map, out var value2))
					{
						value2.add(npcClass);
					}
					else
					{
						MapClass mapClass = new();
						mapClass.MapID = npcClass.Rxjh_Map;
						mapClass.add(npcClass);
						World.Map.Add(mapClass.MapID, mapClass);
					}
					npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
					World.Npc_HaiThuoc_NamLam.Add(npcClass.NPC_SessionID, npcClass);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Add NPC Hái Thuốc số lượng - lỗi [" + int_0 + "]error：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			List<NpcClass> list = new();
			foreach (var value in World.Npc_HaiThuoc_NamLam.Values)
			{
				list.Add(value);
			}
			if (list != null)
			{
				foreach (var item in list)
				{
					item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
				}
				list.Clear();
			}
			World.Npc_HaiThuoc_NamLam.Clear();
			World.Event_HaiThuoc_Progress = 0;
			if (ThoiGian1 != null)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
			}
			if (ThoiGian2 != null)
			{
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
			}
			World.HaiThuoc_NamLam_Event = null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Dispose NPC Thuoc_NamLam: - " + ex.Message);
		}
	}
}
