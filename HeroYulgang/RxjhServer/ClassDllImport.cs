using System;
using System.Runtime.InteropServices;
using HeroYulgang.Helpers;

namespace RxjhServer;

public class ClassDllImport
{
	public delegate int Decrypt(byte[] byte_0, int int_0);

	private static IntPtr instance;

	[DllImport("Kernel32.dll")]
	public static extern IntPtr LoadLibrary(string string_0);

	[DllImport("kernel32.dll", SetLastError = true)]
	public static extern int GetProcAddress(IntPtr intptr_0, string string_0);

	[DllImport("kernel32.dll", SetLastError = true)]
	public static extern bool FreeLibrary(IntPtr intptr_0);

	private static Delegate smethod_0(IntPtr intptr_0, string string_0, Type type_0)
	{
		var procAddress = GetProcAddress(instance, string_0);
		if (procAddress == 0)
		{
			return null;
		}
		return Marshal.GetDelegateForFunctionPointer(new(procAddress), type_0);
	}

	public static void LoadLib()
	{
		instance = LoadLibrary("rxjhDeBuf.dll");
		if (instance.ToInt32() == 0)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "rxjhDeBuf.dll Không tồn tại hoặc không thể tải！");
		}
	}

	public static void FreeLib()
	{
		FreeLibrary(instance);
	}

	public static void DecryptaBK(byte[] byte_0, int int_0)
	{
		try
		{
			((Decrypt)smethod_0(instance, "Decrypt", typeof(Decrypt)))(byte_0, int_0);
		}
		catch
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "rxjhDeBuf.dll Phiên bản sai");
		}
	}
}
