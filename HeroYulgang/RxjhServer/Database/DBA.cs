using Microsoft.EntityFrameworkCore;
using System;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Threading.Tasks;
using HeroYulgang.Database.Entities.Account;
using HeroYulgang.Database.Entities.Game;
using HeroYulgang.Database.Entities.Public;
using Microsoft.Data.SqlClient;
using HeroYulgang.Database.Entities.BBG;

namespace RxjhServer.Database
{
    /// <summary>
    /// Lớp tương thích với DBA cũ, sử dụng Entity Framework Core
    /// </summary>
    public static class DBA
    {
        private static readonly AccountDbContext _accountDbContext = new AccountDbContext();
        private static readonly GameDbContext _gameDbContext = new GameDbContext();
        private static readonly PublicDbContext _publicDbContext = new PublicDbContext();
        private static readonly BBGDbContext _bbgDbContext = new BBGDbContext();

        /// <summary>
        /// L<PERSON>y kết nối cơ sở dữ liệu dựa trên tên
        /// </summary>
        private static DbConnection GetDatabaseConnection(string databaseName)
        {
            switch (databaseName.ToLower())
            {
                case "accountdb":
                case "account":
                case "rxjhaccount":
                    return _accountDbContext.Database.GetDbConnection();

                case "gamedb":
                case "game":
                case "gameserver":
                    return _gameDbContext.Database.GetDbConnection();

                case "publicdb":
                case "public":
                    return _publicDbContext.Database.GetDbConnection();
                case "webdb":
                case "web":
                case "bbg":
                    return _bbgDbContext.Database.GetDbConnection();

                default:
                    throw new ArgumentException($"Không hỗ trợ cơ sở dữ liệu: {databaseName}");
            }
        }

        /// <summary>
        /// Thực thi truy vấn SQL và xử lý kết quả bằng hàm xử lý được cung cấp
        /// </summary>
        private static T ExecuteDbCommand<T>(string sql, SqlParameter[] parameters, string databaseName, Func<DbCommand, T> handler)
        {
            try
            {
                var connection = GetDatabaseConnection(databaseName);
                using var command = connection.CreateCommand();
                command.CommandText = sql;

                if (parameters != null)
                {
                    foreach (var parameter in parameters)
                    {
                        command.Parameters.Add(parameter);
                    }
                }

                connection.Open();
                var result = handler(command);
                connection.Close();

                return result;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thực hiện truy vấn ExecuteDbCommand: {ex.Message} {sql}");
                return default;
            }
        }

        /// <summary>
        /// Lấy dữ liệu từ cơ sở dữ liệu và trả về DataTable
        /// </summary>
        public static DataTable GetDBToDataTable(string sql, string databaseName = "GameDb")
        {
            var dataTable = new DataTable();

            return ExecuteDbCommand(sql, null, databaseName, command =>
            {
                using var reader = command.ExecuteReader();
                dataTable.Load(reader);
                return dataTable;
            }) ?? new DataTable();
        }

        /// <summary>
        /// Lấy dữ liệu từ cơ sở dữ liệu và trả về DataTable với tham số
        /// </summary>
        public static DataTable GetDBToDataTable(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            var dataTable = new DataTable();

            return ExecuteDbCommand(sql, parameters, databaseName, command =>
            {
                using var reader = command.ExecuteReader();
                dataTable.Load(reader);
                return dataTable;
            }) ?? new DataTable();
        }

        /// <summary>
        /// Lấy giá trị đơn từ cơ sở dữ liệu
        /// </summary>
        public static object GetDBValue_3(string sql, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(sql, null, databaseName, command => command.ExecuteScalar());
        }

        public static object GetDBValue_3(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(sql, parameters, databaseName, command => command.ExecuteScalar());
        }

        /// <summary>
        /// Thực thi lệnh SQL không trả về dữ liệu với tham số
        /// </summary>
        public static int ExeSqlCommand(string sql, SqlParameter[] parameters, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(sql, parameters, databaseName, command => command.ExecuteNonQuery());
        }

        /// <summary>
        /// Thực thi lệnh SQL không trả về dữ liệu
        /// </summary>
        public static int ExeSqlCommand(string sql, string databaseName = "GameDb")
        {
            return ExecuteDbCommand(sql, null, databaseName, command => command.ExecuteNonQuery());
        }

    }
}
