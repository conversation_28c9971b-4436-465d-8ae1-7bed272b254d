using System;
using System.Collections.Generic;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

internal class Offline_Treo_May
{
	public static void OfflineTreoMay(Players player)
	{
		try
		{
			if ((int)DateTime.Now.Subtract(player.Auto_Train_Track).TotalMilliseconds >= 3600000)
			{
				var num = RNG.Next(-30, 30);
				var num2 = RNG.Next(-30, 30);
				player.Auto_Train_Track = DateTime.Now;
				player.Mobile(420 + num, 1740 + num2, player.NhanVatToaDo_Z, 101, 0);
			}
			else
			{
				if ((int)DateTime.Now.Subtract(player.Auto_Train_Track).TotalMilliseconds < 12000)
				{
					return;
				}
				if (player.NhanVat_HP <= (int)(player.CharacterMax_HP * 0.7))
				{
					player.AddBlood(20000);
					player.EffectOfTakingMedicine(1000000102);
					player.CapNhat_HP_MP_SP();
				}
				if (player.NhanVat_MP <= (int)(player.CharacterMax_MP * 0.2))
				{
					player.MagicPlus(10000);
					player.EffectOfTakingMedicine(1000000104);
					player.CapNhat_HP_MP_SP();
				}
				if (player.ChucNang_Auto_ThucHien == 1 && (int)DateTime.Now.Subtract(player.Time_Su_Dung_HoTro_ChucNang).TotalMilliseconds >= 60000)
				{
					player.Time_Su_Dung_HoTro_ChucNang = DateTime.Now;
					player.Auto_SuDungPill();
					player.BuffThichKhach();
					player.Buff_CamSu();
					player.Auto_cho_pet_an();
					player.Buff_Cung();
				}
				var num3 = OfflineAutoNhatItem(player);
				if (num3 != 0L && World.ItmeTeM.ContainsKey(num3))
				{
					var parcelVacancyPosition = player.GetParcelVacancyPosition();
					if (player.Offline_TreoMay_Mode_ON_OFF != 0 || parcelVacancyPosition >= 1)
					{
						Offline_TreoMay_VatPham(player, num3);
					}
				}
			}
			var num4 = player.NhanVatToaDo_X - player.Offline_TreoMay_ToaDo_X;
			var num5 = player.NhanVatToaDo_Y - player.Offline_TreoMay_ToaDo_Y;
			double num6 = (int)Math.Sqrt(num4 * (double)num4 + num5 * (double)num5);
			if (player.Offline_TreoMay_BanDo != player.NhanVatToaDo_BanDo)
			{
				player.Mobile(player.Offline_TreoMay_ToaDo_X, player.Offline_TreoMay_ToaDo_Y, player.NhanVatToaDo_Z, player.Offline_TreoMay_BanDo, 0);
				return;
			}
			if ((int)num6 > World.Offline_TreoMay_PhamVi && (int)DateTime.Now.Subtract(player.Auto_Train_Track).TotalMilliseconds >= 12000)
			{
				player.OffLine_TreoMay_CongKich_QuaiVat = 0;
				OfflineMove(player, player.Offline_TreoMay_ToaDo_X, player.Offline_TreoMay_ToaDo_Y, (float)num6);
				return;
			}
			if (player.NhanVat_HP <= 0L || player.PlayerTuVong)
			{
				player.Mobile(player.Offline_TreoMay_ToaDo_X, player.Offline_TreoMay_ToaDo_Y, player.NhanVatToaDo_Z, player.Offline_TreoMay_BanDo, 0);
				player.NhanVat_HP = player.CharacterMax_HP;
				player.CapNhat_HP_MP_SP();
				player.PlayerTuVong = false;
				return;
			}
			if (player.Player_Job == 5 && player.offline_buff)
			{
				try
				{
					player.OffLine_TreoMay_CongKich_QuaiVat = player.SessionID;
					player.Auto_TreoMay_TheoDoi_ThoiGian = 0;
					if (X_Vo_Cong_Loai.GetsfeWg(player, 501203))
					{
						player.OfflineTreoMaySkill_ID = 501203;
					}
					if (X_Vo_Cong_Loai.GetsfeWg(player, 501502))
					{
						player.offline_buff_1 = 501502;
					}
					if (X_Vo_Cong_Loai.GetsfeWg(player, 501601))
					{
						player.offline_buff_2 = 501601;
					}
					if (X_Vo_Cong_Loai.GetsfeWg(player, 501501))
					{
						player.offline_buff_3 = 501501;
					}
					if (X_Vo_Cong_Loai.GetsfeWg(player, 501603))
					{
						player.offline_buff_4 = 501603;
					}
					if (X_Vo_Cong_Loai.GetsfeWg(player, 501602))
					{
						player.offline_buff_5 = 501602;
					}
					if (player.offline_buff_1 > 0 && !player.AppendStatusList.ContainsKey(player.offline_buff_1) && !player.AppendStatusList.ContainsKey(501303))
					{
						player.OfflineTreoMaySkill_ID = player.offline_buff_1;
					}
					else if (player.offline_buff_2 > 0 && !player.AppendStatusList.ContainsKey(player.offline_buff_2) && !player.AppendStatusList.ContainsKey(501403))
					{
						player.OfflineTreoMaySkill_ID = player.offline_buff_2;
					}
					else if (player.offline_buff_3 > 0 && !player.AppendStatusList.ContainsKey(player.offline_buff_3) && !player.AppendStatusList.ContainsKey(501301))
					{
						player.OfflineTreoMaySkill_ID = player.offline_buff_3;
					}
					else if (player.offline_buff_4 > 0 && !player.AppendStatusList.ContainsKey(player.offline_buff_4) && !player.AppendStatusList.ContainsKey(501402))
					{
						player.OfflineTreoMaySkill_ID = player.offline_buff_4;
					}
					else if (player.offline_buff_5 > 0 && !player.AppendStatusList.ContainsKey(player.offline_buff_5) && !player.AppendStatusList.ContainsKey(501401))
					{
						player.OfflineTreoMaySkill_ID = player.offline_buff_5;
					}
				}
				catch
				{
					LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Đại Phu chưa học - Sách:[" + player.OfflineTreoMaySkill_ID + "]-[" + player.Userid + "][" + player.UserName + "]");
				}
				OfflineTrainLevel(player);
				if ((int)DateTime.Now.Subtract(player.Offline_Auto_Attack_Time).TotalMilliseconds >= 1500)
				{
					player.Offline_Auto_Attack_Time = DateTime.Now;
				}
				return;
			}
			var npc = MapClass.GetNpc(player.NhanVatToaDo_BanDo, player.OffLine_TreoMay_CongKich_QuaiVat);
			if (npc == null || npc.NPCDeath || npc.Rxjh_HP <= 0)
			{
				player.OffLine_TreoMay_CongKich_QuaiVat = int_AutomaticAttackPhamViNpc(player, player.Offline_TreoMay_ToaDo_X, player.Offline_TreoMay_ToaDo_Y);
				if (player.OffLine_TreoMay_CongKich_QuaiVat <= 10000)
				{
					return;
				}
			}
			while (true)
			{
				var npc2 = MapClass.GetNpc(player.NhanVatToaDo_BanDo, player.OffLine_TreoMay_CongKich_QuaiVat);
				if (npc2 == null)
				{
					break;
				}
				var num7 = player.NhanVatToaDo_X - npc2.Rxjh_X;
				var num8 = player.NhanVatToaDo_Y - npc2.Rxjh_Y;
				double num9 = (int)Math.Sqrt(num7 * (double)num7 + num8 * (double)num8);
				if ((int)num9 <= 30)
				{
					break;
				}
				player.OffLine_TreoMay_CongKich_QuaiVat = int_AutomaticAttackPhamViNpc(player, player.Offline_TreoMay_ToaDo_X, player.Offline_TreoMay_ToaDo_Y);
				if (player.OffLine_TreoMay_CongKich_QuaiVat <= 10000)
				{
					return;
				}
			}
			player.Auto_TreoMay_TheoDoi_ThoiGian = 0;
			OfflineTrainLevel(player);
			if ((int)DateTime.Now.Subtract(player.Offline_Auto_Attack_Time).TotalMilliseconds >= 1500)
			{
				player.Offline_Auto_Attack_Time = DateTime.Now;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Auto Offline train quái bị lỗi !! : [" + player.Userid + "][" + player.UserName + "]-Job: [" + player.Player_Job + "] - [" + ex.Message);
		}
	}

	public static void Offline_TreoMay_VatPham(Players player, long dmwpid)
	{
		try
		{
			var string_ = "AA5517002C010B000800C676600000000000000000000000000055AA";
			var array = Converter.HexStringToByte(string_);
			System.Buffer.BlockCopy(BitConverter.GetBytes(dmwpid), 0, array, 10, 8);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			player.PickUpItems(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi Offline TreoMay VatPham - " + ex.Message);
		}
	}

	public static long OfflineAutoNhatItem(Players player)
	{
		var result = 0L;
		List<X_Mat_Dat_Vat_Pham_Loai> list = new();
		Random random = new();
		foreach (var value in World.ItmeTeM.Values)
		{
			try
			{
				if (player.TraTim_PhamVi_Drop_VatPham(50, value) && value.VatPham_QuyenUuTien != null && value.VatPham_QuyenUuTien.UserName == player.UserName)
				{
					list.Add(value);
				}
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi Offline Auto Nhat Item - " + ex.Message);
				throw;
			}
		}
		if (list.Count > 0)
		{
			var index = random.Next(0, list.Count);
			result = list[index].VatPham.GetItemGlobal_ID;
			list.Clear();
			return result;
		}
		list.Clear();
		return result;
	}

	public static void Packet_SuDungPill_V20(Players player, int PID)
	{
		try
		{
			var parcelVacancyNumber_Test = player.GetParcelVacancyNumber_Test(PID);
			if (parcelVacancyNumber_Test >= 0)
			{
				var string_ = "AA5522002C013A001C0001000037DC143C00000000000000000000000000010000000000000055AA";
				var array = Converter.HexStringToByte(string_);
				System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
				System.Buffer.BlockCopy(BitConverter.GetBytes(parcelVacancyNumber_Test), 0, array, 11, 1);
				System.Buffer.BlockCopy(BitConverter.GetBytes(PID), 0, array, 14, 4);
				player.OpenItem(array, array.Length);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi Packet_SuDungPill_V20 - " + ex.Message);
		}
	}

	public static void OfflineTrainLevel(Players player)
	{
		try
		{
			var array = Converter.HexStringToByte("AA551E002E01090018005828B3C300000000AE705F4300007041B82D35441B2700000100000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.OffLine_TreoMay_CongKich_QuaiVat), 0, array, 10, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.OfflineTreoMaySkill_ID), 0, array, 14, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.NhanVatToaDo_X), 0, array, 18, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.NhanVatToaDo_Z), 0, array, 22, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.NhanVatToaDo_Y), 0, array, 26, 4);
			player.Attack(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi Offline Train Level - " + ex.Message);
		}
	}

	public static void OfflineMove(Players player, float x, float y, float jl)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E002E010700280002000000F91DC7426177ACC3978E3C44F91DC74200007041978E3C4401050000000000005C28000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.SessionID), 0, array, 4, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(x), 0, array, 14, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(15f), 0, array, 18, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(y), 0, array, 22, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.NhanVatToaDo_X), 0, array, 26, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.NhanVatToaDo_Z), 0, array, 30, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.NhanVatToaDo_Y), 0, array, 34, 4);
			System.Buffer.BlockCopy(BitConverter.GetBytes(player.Character_KhinhCong), 0, array, 39, 1);
			System.Buffer.BlockCopy(BitConverter.GetBytes(jl), 0, array, 42, 4);
			player.CharacterMove(array, array.Length);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi OfflineMove - " + ex.Message);
		}
	}

	public static void AutoHP(Players player)
	{
	}

	public static void AutoMP(Players player)
	{
	}

	public static int int_AutomaticAttackPhamViNpc(Players player, float float_0, float float_1)
	{
		if (player.NpcList != null)
		{
			try
			{
				List<NpcClass> list = new();
				foreach (var value in player.NpcList.Values)
				{
					if (bool_AutomaticAttackPhamViNpc(player, World.Offline_TreoMay_PhamVi, value, float_0, float_1) && !value.NPCDeath && value.IsNpc != 1 && value.Rxjh_HP > 1L)
					{
						list.Add(value);
					}
				}
				if (list.Count > 0)
				{
					var result = 0;
					var num = World.Offline_TreoMay_PhamVi;
					foreach (var item in list)
					{
						var num2 = item.Rxjh_X - player.NhanVatToaDo_X;
						var num3 = item.Rxjh_Y - player.NhanVatToaDo_Y;
						var num4 = (float)Math.Sqrt(num2 * num2 + num3 * num3);
						if ((int)num4 < num)
						{
							num = (int)num4;
							result = item.NPC_SessionID;
						}
					}
					list.Clear();
					return result;
				}
				list.Clear();
			}
			catch (Exception ex)
			{
				LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error, "Lỗi int Automatic Attack PhamVi Npc - " + ex.Message);
			}
		}
		return 0;
	}

	public static bool bool_AutomaticAttackPhamViNpc(Players player, int int_75, NpcClass npcClass_0, float float_0, float float_1)
	{
		if (npcClass_0.Rxjh_Map != player.NhanVatToaDo_BanDo)
		{
			return false;
		}
		if (player.NhanVatToaDo_BanDo == 7101)
		{
			int_75 = 1000;
		}
		var num = npcClass_0.Rxjh_X - float_0;
		var num2 = npcClass_0.Rxjh_Y - float_1;
		float num3 = (int)Math.Sqrt(num * num + num2 * num2);
		return num3 <= int_75;
	}
}
