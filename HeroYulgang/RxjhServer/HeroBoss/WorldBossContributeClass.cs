﻿using RxjhServer.ManageZone;
using System.Collections.Generic;
using HeroYulgang.Helpers;

namespace RxjhServer.HeroBoss;
public class WorldBossContributeClass
{
    public int ID;
    public NpcClass Boss;
    public Dictionary<(int,int), DamageContribute> Contribute;
    public Zone currentZone;
    private string _KilledBy;
    public bool Rewarded { get; set; }
    private WorldBossReward[] _reward;
    public WorldBossReward[] Reward
    {
        get => _reward;
        set => _reward = value;
    }


    public string KilledBy
    {
        get => _KilledBy;
        set => _KilledBy = value;
    }

    public void UpdateContribute(int worldId,int SessionID,string playerName, long damage, int attackCount)
    {
        // Check and add if player is not in the dictionary
        LogHelper.WriteLine(HeroYulgang.Services.LogLevel.Error,"Cập nhật contribute cho "+playerName + " DMG : "+ damage);
        if (!Contribute.ContainsKey((World.ServerID,SessionID)))
        {
            Contribute.Add((World.ServerID,SessionID), new DamageContribute(worldId,SessionID,playerName, damage, attackCount));
        }
        else
        {
            Contribute[(World.ServerID,SessionID)].Damage += damage;
            Contribute[(World.ServerID,SessionID)].AttackCount += attackCount;
        }
    }

    public DamageContribute GetContributeByPlayer(Players player)
    {
        if (Contribute.ContainsKey((World.ServerID,player.SessionID)))
        {
            return Contribute[(World.ServerID,player.SessionID)];
        }
        return null;
    }
}

public class WorldBossReward
{
    public int ID;
    public int RewardID;
    public int RewardCount;
    public Players player;
}
